import pandas as pd
import numpy as np
from rapidfuzz import process, fuzz
import os

# Define file paths
target_file_path = "C:\\Users\\<USER>\\OneDrive - Adastra, s.r.o\\Desktop\\Fuzzy Match Target - ENT GFC Propensity List OCT.xlsx"
account_list_file_path = "C:\\Users\\<USER>\\Downloads\\JT - CA & US Accounts 2025_05_02 2-44-05 PM.xlsx"
output_file_path = "C:\\Users\\<USER>\\OneDrive - Adastra, s.r.o\\Desktop\\Fuzzy Match Target - ENT GFC Propensity List OCT - Updated.xlsx"

print(f"🔍 Loading target file: {os.path.basename(target_file_path)}")
print(f"🔍 Loading account list file: {os.path.basename(account_list_file_path)}")

# Load the Excel files with all sheets
try:
    # Try to read the target file
    target_excel = pd.ExcelFile(target_file_path)
    target_sheet_names = target_excel.sheet_names
    print(f"📊 Target file sheets: {target_sheet_names}")

    # Read the first sheet by default
    target_df = pd.read_excel(target_excel, sheet_name=0)
    print(f"📋 Target file columns: {target_df.columns.tolist()}")

    # Try to read the account list file
    account_excel = pd.ExcelFile(account_list_file_path)
    account_sheet_names = account_excel.sheet_names
    print(f"📊 Account list file sheets: {account_sheet_names}")

    # Read the first sheet by default
    account_df = pd.read_excel(account_excel, sheet_name=0)
    print(f"📋 Account list file columns: {account_df.columns.tolist()}")

 except Exception as e:
    print(f"❌ Error loading files: {e}")
    exit(1)

# Ask user to confirm column names for company names in both files
print("\n" + "-"*50)
print("Please confirm the column names for matching:")

# Guess the company name column in target file
target_company_col = None
for col in target_df.columns:
    if 'company' in col.lower() or 'account' in col.lower() or 'customer' in col.lower() or 'name' in col.lower() or 'organization' in col.lower():
        target_company_col = col
        break

if target_company_col:
    print(f"Suggested company name column in target file: '{target_company_col}'")
else:
    print("Could not guess company name column in target file.")
    print("Available columns: " + ", ".join([f"'{col}'" for col in target_df.columns]))

# Guess the company name column in account list file
account_company_col = None
for col in account_df.columns:
    if 'company' in col.lower() or 'account' in col.lower() or 'customer' in col.lower() or 'name' in col.lower() or 'organization' in col.lower():
        account_company_col = col
        break

if account_company_col:
    print(f"Suggested company name column in account list file: '{account_company_col}'")
else:
    print("Could not guess company name column in account list file.")
    print("Available columns: " + ", ".join([f"'{col}'" for col in account_df.columns]))

# Guess the AE name column in account list file
account_ae_col = None
for col in account_df.columns:
    if 'ae' in col.lower() or 'owner' in col.lower() or 'rep' in col.lower() or 'manager' in col.lower() or 'adastra' in col.lower():
        account_ae_col = col
        break

if account_ae_col:
    print(f"Suggested AE name column in account list file: '{account_ae_col}'")
else:
    print("Could not guess AE name column in account list file.")
    print("Available columns: " + ", ".join([f"'{col}'" for col in account_df.columns]))

print("-"*50)

# Ask for confirmation or correction
target_company_col_input = input(f"Enter company name column in target file [{target_company_col}]: ").strip()
if target_company_col_input:
    target_company_col = target_company_col_input

account_company_col_input = input(f"Enter company name column in account list file [{account_company_col}]: ").strip()
if account_company_col_input:
    account_company_col = account_company_col_input

account_ae_col_input = input(f"Enter AE name column in account list file [{account_ae_col}]: ").strip()
if account_ae_col_input:
    account_ae_col = account_ae_col_input

# Confirm the target column for AE name in the target file
target_ae_col = input("Enter column name to store AE names in target file [Adastra AE]: ").strip()
if not target_ae_col:
    target_ae_col = "Adastra AE"

# Prepare dataframes for matching
print("\n🔄 Preparing data for fuzzy matching...")

# Extract and clean company names from both files
target_df_cleaned = target_df[[target_company_col]].copy().dropna(subset=[target_company_col])
target_df_cleaned['original_index'] = target_df_cleaned.index
target_df_cleaned['clean_name'] = target_df_cleaned[target_company_col].str.lower().str.strip()

account_df_cleaned = account_df[[account_company_col, account_ae_col]].copy().dropna(subset=[account_company_col])
account_df_cleaned['clean_name'] = account_df_cleaned[account_company_col].str.lower().str.strip()

# Perform fuzzy matching
print("� Performing fuzzy matching...")
matches = []
threshold = 80  # Minimum match score to consider

for idx, row in target_df_cleaned.iterrows():
    company_name = row['clean_name']
    match_result = process.extractOne(company_name, account_df_cleaned['clean_name'].tolist(), scorer=fuzz.token_sort_ratio)

    if match_result and match_result[1] >= threshold:
        match_name, score, match_idx = match_result
        ae_name = account_df_cleaned.iloc[match_idx][account_ae_col]
        original_idx = row['original_index']

        matches.append({
            'Target Company': row[target_company_col],
            'Matched Account': account_df_cleaned.iloc[match_idx][account_company_col],
            'Match Score': score,
            'AE Name': ae_name,
            'Target Index': original_idx
        })
    else:
        original_idx = row['original_index']
        matches.append({
            'Target Company': row[target_company_col],
            'Matched Account': 'No match found',
            'Match Score': match_result[1] if match_result else 0,
            'AE Name': '',
            'Target Index': original_idx
        })

# Convert matches to DataFrame
matches_df = pd.DataFrame(matches)
print(f"✅ Found {len(matches_df[matches_df['Match Score'] >= threshold])} matches above threshold.")

# Create a copy of the target file and add AE names
target_df_updated = target_df.copy()

# Add AE column if it doesn't exist
if target_ae_col not in target_df_updated.columns:
    target_df_updated[target_ae_col] = ''

# Update the AE names in the target file
for _, match in matches_df.iterrows():
    if match['Match Score'] >= threshold:
        target_df_updated.at[match['Target Index'], target_ae_col] = match['AE Name']

# Save the updated target file
print(f"� Saving updated target file to: {os.path.basename(output_file_path)}")
try:
    # Create a writer with the target Excel file
    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
        # Write the updated dataframe to the same sheet
        target_df_updated.to_excel(writer, sheet_name=target_sheet_names[0], index=False)

        # Write the matches to a new sheet
        matches_df.to_excel(writer, sheet_name='Matching Results', index=False)

    print(f"✅ Successfully updated target file with AE names!")
    print(f"✅ Matching results also saved in a separate sheet.")
except Exception as e:
    print(f"❌ Error saving updated file: {e}")

    # Try to save just the matches as a separate file
    try:
        matches_df.to_excel("Fuzzy_Matching_Results.xlsx", index=False)
        print("✅ Matching results saved as 'Fuzzy_Matching_Results.xlsx'.")
    except:
        print("❌ Could not save matching results.")

