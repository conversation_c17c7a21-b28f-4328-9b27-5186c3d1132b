import pandas as pd
from rapidfuzz import process, fuzz
import os
import sys
import datetime

def main():
    # Define file paths
    target_file_path = r"C:\Users\<USER>\Downloads\Fuzzy Match Target - ENT GFC Propensity List OCT.xlsx"
    account_list_file_path = r"C:\Users\<USER>\Downloads\JT - CA & US Accounts 2025_05_02 2-44-05 PM.xlsx"
    output_file_path = r"C:\Users\<USER>\Downloads\Fuzzy Match Target - ENT GFC Propensity List OCT - Updated.xlsx"

    print(f"🔍 Loading target file: {os.path.basename(target_file_path)}")
    print(f"🔍 Loading account list file: {os.path.basename(account_list_file_path)}")

    # Load the Excel files
    try:
        # Check if files exist
        if not os.path.exists(target_file_path):
            print(f"❌ Target file not found: {target_file_path}")
            sys.exit(1)

        if not os.path.exists(account_list_file_path):
            print(f"❌ Account list file not found: {account_list_file_path}")
            sys.exit(1)

        # Load target file - Sheet1
        try:
            target_df = pd.read_excel(target_file_path, sheet_name="Sheet1")
            print(f"📋 Target file loaded successfully with {len(target_df)} rows")
        except Exception as e:
            print(f"❌ Error loading target file: {e}")
            # List available sheets
            xls = pd.ExcelFile(target_file_path)
            print(f"Available sheets in target file: {xls.sheet_names}")
            sheet_name = input("Enter the correct sheet name for target file: ")
            if not sheet_name:
                print("No sheet name provided. Exiting.")
                sys.exit(1)
            target_df = pd.read_excel(target_file_path, sheet_name=sheet_name)
            print(f"📋 Target file loaded successfully with {len(target_df)} rows")

        # Load account list file - JT - CA & US Accounts
        try:
            account_df = pd.read_excel(account_list_file_path, sheet_name="JT - CA & US Accounts")
            print(f"📋 Account list file loaded successfully with {len(account_df)} rows")
        except Exception as e:
            print(f"❌ Error loading account list file: {e}")
            # List available sheets
            xls = pd.ExcelFile(account_list_file_path)
            print(f"Available sheets in account list file: {xls.sheet_names}")
            sheet_name = input("Enter the correct sheet name for account list file: ")
            if not sheet_name:
                print("No sheet name provided. Exiting.")
                sys.exit(1)
            account_df = pd.read_excel(account_list_file_path, sheet_name=sheet_name)
            print(f"📋 Account list file loaded successfully with {len(account_df)} rows")

    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

    # Define column names
    target_company_col = "gtm_customer_name"
    target_ae_col = "Adastra AE Name"
    account_company_col = "Account Name"
    account_ae_col = "Owner"

    # Verify columns exist and prompt for correct column names if needed
    # Check target company column
    if target_company_col not in target_df.columns:
        print(f"❌ Column '{target_company_col}' not found in target file")
        print("Available columns in target file:", target_df.columns.tolist())
        new_col = input(f"Enter the correct column name for company names in target file: ")
        if new_col and new_col in target_df.columns:
            target_company_col = new_col
        else:
            print("Invalid column name. Exiting.")
            sys.exit(1)

    # Check target AE column
    if target_ae_col not in target_df.columns:
        print(f"❌ Column '{target_ae_col}' not found in target file")
        print("Available columns in target file:", target_df.columns.tolist())
        new_col = input(f"Enter the correct column name for AE names in target file (or press Enter to create it): ")
        if new_col and new_col in target_df.columns:
            target_ae_col = new_col
        else:
            print(f"Creating new column '{target_ae_col}' in target file")
            target_df[target_ae_col] = ""

    # Check account company column
    if account_company_col not in account_df.columns:
        print(f"❌ Column '{account_company_col}' not found in account list file")
        print("Available columns in account list file:", account_df.columns.tolist())
        new_col = input(f"Enter the correct column name for company names in account list file: ")
        if new_col and new_col in account_df.columns:
            account_company_col = new_col
        else:
            print("Invalid column name. Exiting.")
            sys.exit(1)

    # Check account AE column
    if account_ae_col not in account_df.columns:
        print(f"❌ Column '{account_ae_col}' not found in account list file")
        print("Available columns in account list file:", account_df.columns.tolist())
        new_col = input(f"Enter the correct column name for owner/AE names in account list file: ")
        if new_col and new_col in account_df.columns:
            account_ae_col = new_col
        else:
            print("Invalid column name. Exiting.")
            sys.exit(1)

    # Prepare dataframes for matching
    print("\n🔄 Preparing data for fuzzy matching...")

    # Clean company names for matching
    print("Cleaning and preparing company names for matching...")

    # Handle potential NaN values in target file
    target_df_cleaned = target_df.copy()
    target_df_cleaned[target_company_col] = target_df_cleaned[target_company_col].fillna('')
    target_df_cleaned['clean_name'] = target_df_cleaned[target_company_col].astype(str).str.lower().str.strip()

    # Remove rows with empty company names
    target_df_cleaned = target_df_cleaned[target_df_cleaned['clean_name'] != '']
    target_df_cleaned = target_df_cleaned[target_df_cleaned['clean_name'] != 'nan']
    print(f"Target file has {len(target_df_cleaned)} rows with valid company names")

    # Handle potential NaN values in account list file
    account_df_cleaned = account_df.copy()
    account_df_cleaned[account_company_col] = account_df_cleaned[account_company_col].fillna('')
    account_df_cleaned[account_ae_col] = account_df_cleaned[account_ae_col].fillna('')
    account_df_cleaned['clean_name'] = account_df_cleaned[account_company_col].astype(str).str.lower().str.strip()

    # Remove rows with empty company names or owner names
    account_df_cleaned = account_df_cleaned[account_df_cleaned['clean_name'] != '']
    account_df_cleaned = account_df_cleaned[account_df_cleaned['clean_name'] != 'nan']
    print(f"Account list file has {len(account_df_cleaned)} rows with valid company names")

    # Create a dictionary of account names to owner names for faster lookup
    account_dict = dict(zip(account_df_cleaned['clean_name'], account_df_cleaned[account_ae_col]))

    # Ask user for threshold
    threshold_input = input("Enter match threshold (0-100, default 80): ").strip()
    threshold = 80  # Default threshold
    if threshold_input and threshold_input.isdigit():
        threshold = int(threshold_input)
        if threshold < 0 or threshold > 100:
            print("Invalid threshold. Using default value of 80.")
            threshold = 80
    print(f"Using match threshold: {threshold}")

    # Perform fuzzy matching
    print("🔄 Performing fuzzy matching...")
    matches = []

    # Create a list of all account names for matching
    all_account_names = account_df_cleaned['clean_name'].tolist()
    if not all_account_names:
        print("No valid account names found for matching. Exiting.")
        sys.exit(1)

    # Track statistics
    match_count = 0
    no_match_count = 0
    total_rows = len(target_df_cleaned)

    # Show progress
    print(f"Processing {total_rows} companies...")

    # Process each row in the target file
    for idx, row in target_df_cleaned.iterrows():
        company_name = row['clean_name']

        # Skip empty values (should be already filtered out)
        if not company_name or company_name == "nan":
            continue

        # Perform fuzzy matching
        try:
            match_result = process.extractOne(company_name, all_account_names, scorer=fuzz.token_sort_ratio)

            if match_result and match_result[1] >= threshold:
                match_name, score, match_idx = match_result
                ae_name = account_dict.get(match_name, "")

                # Update the AE name in the target dataframe
                original_idx = row.name  # Get the original index in the target dataframe
                target_df.at[original_idx, target_ae_col] = ae_name

                matches.append({
                    'Target Company': row[target_company_col],
                    'Matched Account': account_df_cleaned.iloc[match_idx][account_company_col],
                    'Match Score': score,
                    'AE Name': ae_name
                })
                match_count += 1

                # Print progress every 10 matches
                if match_count % 10 == 0:
                    print(f"Processed {match_count + no_match_count} of {total_rows} companies. Found {match_count} matches so far.")
            else:
                matches.append({
                    'Target Company': row[target_company_col],
                    'Matched Account': 'No match found',
                    'Match Score': match_result[1] if match_result else 0,
                    'AE Name': ''
                })
                no_match_count += 1
        except Exception as e:
            print(f"Error matching company '{company_name}': {e}")
            matches.append({
                'Target Company': row[target_company_col],
                'Matched Account': 'Error during matching',
                'Match Score': 0,
                'AE Name': ''
            })
            no_match_count += 1

    # Convert matches to DataFrame
    matches_df = pd.DataFrame(matches)
    print(f"✅ Found {match_count} matches above threshold.")
    print(f"❌ No match found for {no_match_count} companies.")

    # Save the updated target file
    print(f"💾 Saving updated target file to: {os.path.basename(output_file_path)}")

    # Create a backup of the original file
    backup_path = target_file_path.replace('.xlsx', f'_backup_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
    try:
        import shutil
        shutil.copy2(target_file_path, backup_path)
        print(f"Created backup of original file at: {os.path.basename(backup_path)}")
    except Exception as e:
        print(f"Warning: Could not create backup: {e}")

    try:
        # Create a writer with the target Excel file
        with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
            # Write the updated dataframe to the same sheet
            target_df.to_excel(writer, sheet_name="Sheet1", index=False)

            # Write the matches to a new sheet
            matches_df.to_excel(writer, sheet_name='Matching Results', index=False)

            # Add a summary sheet
            summary_data = [
                ['Fuzzy Matching Summary', ''],
                ['Date', datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                ['Target File', os.path.basename(target_file_path)],
                ['Account List File', os.path.basename(account_list_file_path)],
                ['Match Threshold', str(threshold)],
                ['Total Companies Processed', str(total_rows)],
                ['Matches Found', str(match_count)],
                ['No Matches', str(no_match_count)],
                ['Match Rate', f"{match_count/total_rows*100:.2f}%" if total_rows > 0 else "0%"]
            ]

            summary_df = pd.DataFrame(summary_data, columns=['Metric', 'Value'])
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

        print(f"✅ Successfully updated target file with AE names!")
        print(f"✅ Matching results and summary saved in separate sheets.")

        # Also save a separate copy of just the matches
        matches_file = os.path.join(os.path.dirname(output_file_path), f"Fuzzy_Matching_Results_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        matches_df.to_excel(matches_file, index=False)
        print(f"✅ Matching results also saved separately as: {os.path.basename(matches_file)}")

    except Exception as e:
        print(f"❌ Error saving updated file: {e}")

        # Try to save just the matches as a separate file
        try:
            fallback_file = f"Fuzzy_Matching_Results_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            matches_df.to_excel(fallback_file, index=False)
            print(f"✅ Matching results saved as '{fallback_file}'.")
        except Exception as e2:
            print(f"❌ Could not save matching results: {e2}")

    print("\n" + "="*50)
    print("MATCHING SUMMARY")
    print("="*50)
    print(f"Total companies processed: {total_rows}")
    print(f"Matches found: {match_count} ({match_count/total_rows*100:.2f}% match rate)")
    print(f"No matches found: {no_match_count}")
    print("="*50)

if __name__ == "__main__":
    main()
