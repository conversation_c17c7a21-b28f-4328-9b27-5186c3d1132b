from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup
import pandas as pd
import time
import os
import datetime

# Create a class to handle the extraction process
class FundRequestExtractor:
    def __init__(self, headless=False, storage_state="aws_session.json"):
        self.headless = headless
        self.storage_state = storage_state
        self.records = []

    def run(self):
        """Main method to run the extraction process"""
        with sync_playwright() as p:
            # Launch browser and set up context
            browser = p.chromium.launch(headless=self.headless)
            context = browser.new_context(storage_state=self.storage_state)
            page = context.new_page()

            try:
                # Navigate to dashboard and extract data
                print("Starting extraction process...")
                self._navigate_to_dashboard(page)
                self._process_fund_requests(page, context)

                # Save data to Excel
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                self._save_to_excel(timestamp=timestamp)
                print(f"Extraction complete! Found {len(self.records)} records.")
                print(f"Data saved to fund_requests_data_{timestamp}.xlsx and fund_requests_data_{timestamp}.csv")

            except Exception as e:
                print(f"Error in main process: {e}")
                # Log the error and continue

            finally:
                print("Closing browser...")
                browser.close()

    def _navigate_to_dashboard(self, page):
        """Navigate to the funding dashboard and wait for it to load"""
        print("Navigating to Funding Dashboard...")
        page.goto("https://funding.awspartner.com/dashboard")

        # Wait for navigation and authentication to complete
        page.wait_for_load_state("networkidle", timeout=30000)

        # Check if we're on the correct page
        current_url = page.url

        if "/auth" in current_url:
            print("Still on authentication page. Waiting for redirect...")
            # Wait for redirect to dashboard
            page.wait_for_url("**/dashboard", timeout=30000)

        # Additional wait to ensure page is fully rendered
        time.sleep(5)

    def _navigate_to_next_page(self, page):
        """Navigate to the next page of fund requests"""
        print("Looking for next page button...")

        # Based on the screenshot, we need to find the numeric pagination buttons (1, 2, 3, etc.)
        # First, try to find all page number buttons
        try:
            # Look for the pagination container - based on the screenshot
            print("Looking for pagination container...")
            pagination_containers = [
                page.locator("ul[class*='awsui-pagination']"),
                page.locator("ul[class*='pagination']"),
                page.locator(".awsui-pagination-container"),
                page.locator(".pagination"),
            ]

            pagination = None
            for container in pagination_containers:
                if container.count() > 0:
                    pagination = container
                    print(f"Found pagination container")
                    break

            if pagination:
                # Take a screenshot of the pagination area for debugging
                try:
                    pagination.screenshot(path="pagination.png")
                    print("Saved screenshot of pagination as pagination.png")
                except Exception as e:
                    print(f"Could not save pagination screenshot: {e}")

                # Find all page buttons
                page_buttons = page.locator("li[class*='pagination-page-item']").all()
                print(f"Found {len(page_buttons)} pagination buttons")

                # Find which one is active/current
                current_page_num = None
                for button in page_buttons:
                    # Check if this button has the active class
                    class_name = button.get_attribute("class")
                    if class_name and ("active" in class_name or "selected" in class_name):
                        try:
                            current_page_num = int(button.text_content().strip())
                            print(f"Current active page is {current_page_num}")
                            break
                        except:
                            pass

                # If we found the current page, look for the next page button
                if current_page_num is not None:
                    next_page_num = current_page_num + 1
                    print(f"Looking for page number {next_page_num}")

                    # Try to find the button with the next page number
                    for button in page_buttons:
                        try:
                            page_num = int(button.text_content().strip())
                            if page_num == next_page_num:
                                print(f"Found button for page {next_page_num}, clicking...")
                                button.click()
                                time.sleep(2)  # Wait for page to update
                                return True
                        except:
                            pass

                # If we couldn't find the next page by number, try to find a non-active button
                try:
                    non_active_button = page.locator("li[class*='pagination-page-item']:not([class*='active'])").first
                    if non_active_button.count() > 0:
                        print("Found a non-active pagination button, clicking...")
                        non_active_button.click()
                        time.sleep(2)  # Wait for page to update
                        return True
                except Exception as e:
                    print(f"Error finding non-active button: {e}")
        except Exception as e:
            print(f"Error finding pagination by page numbers: {e}")

        # If the above methods fail, try these alternative approaches
        next_page_locators = [
            # Try by the red box in the screenshot
            page.locator("li[class*='pagination-page-item']").nth(1),  # The second page button
            # Try by the '>' character which might be in the pagination
            page.locator("li:has-text('>')"),
            # Try by SVG icon name (angle-right) which might be in the pagination
            page.locator("span[class*='icon']"),
            # Try by numeric page buttons
            page.locator("li:has-text('2')"),
            page.locator("li:has-text('3')"),
            # Try by the Next button if it exists
            page.locator("button:has-text('Next')"),
        ]

        for i, locator in enumerate(next_page_locators):
            try:
                if locator.count() > 0 and locator.is_visible():
                    print(f"Found next page button (method {i+1}), clicking...")
                    locator.click()
                    time.sleep(2)  # Wait for page to update
                    return True
            except Exception as e:
                print(f"Error with locator {i+1}: {e}")

        # One last attempt - try to find any clickable element that might be a page number
        try:
            # Look for any elements with numbers 2-9 that might be page numbers
            for num in range(2, 10):
                number_element = page.locator(f"text='{num}'").first
                if number_element.count() > 0 and number_element.is_visible():
                    print(f"Found element with text '{num}', trying to click it...")
                    number_element.click()
                    time.sleep(2)  # Wait for page to update
                    return True
        except Exception as e:
            print(f"Error finding numeric elements: {e}")

        print("Could not find next page button using any method")
        return False

    def _find_funding_activities_table(self, page):
        """Find the Funding Activities table in the dashboard and return the rows"""
        print("Looking for Funding Activities table in dashboard...")

        # Try to find the table with multiple attempts
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                # First try to find the Funding Activities heading
                print("Looking for 'Funding Activities' heading...")

                # Try different selectors to find the Funding Activities heading
                heading_selectors = [
                    "text=Funding Activities",
                    "span:has-text('Funding Activities')",
                    "h2:has-text('Funding Activities')",
                    "div:has-text('Funding Activities')",
                ]

                heading = None
                for selector in heading_selectors:
                    heading_locator = page.locator(selector)
                    if heading_locator.count() > 0:
                        heading = heading_locator
                        print(f"Found 'Funding Activities' heading with selector: {selector}")
                        break

                if not heading:
                    print(f"Attempt {attempt+1}/{max_attempts}: 'Funding Activities' heading not found, waiting...")
                    time.sleep(5)
                    continue

                # Find the container that holds the table
                print("Looking for the table container...")
                container = None

                try:
                    # First try: Find ancestor div with ID
                    container = heading.locator("xpath=ancestor::div[@id][1]")
                    if container.count() == 0:
                        # Second try: Find closest div containing a table
                        container = heading.locator("xpath=ancestor::div[.//table][1]")
                    if container.count() == 0:
                        # Third try: Just find any table on the page
                        container = page.locator("table").first.locator("xpath=ancestor::div[1]")
                except Exception as e:
                    print(f"Error finding container: {e}")
                    # Fallback: use the page as container
                    container = page

                # Try to get container ID for debugging
                try:
                    container_id = container.get_attribute('id')
                    print(f"Container ID: {container_id if container_id else 'NOT FOUND'}")
                except:
                    print("Could not get container ID")

                # Find the table within the container
                table = container.locator("table").first
                if table.count() == 0:
                    print("No table found in the container, retrying...")
                    time.sleep(5)
                    continue

                # Find the table rows
                fund_rows = table.locator("tbody tr")
                row_count = fund_rows.count()
                print(f"📌 Found {row_count} rows in the Funding Activities table")

                if row_count > 0:
                    # Check if rows have links (Fund Request IDs)
                    has_links = False
                    for i in range(min(3, row_count)):  # Check first 3 rows
                        row = fund_rows.nth(i)
                        links = row.locator("a")
                        if links.count() > 0:
                            has_links = True
                            print(f"Row {i+1} has links: {links.count()}")
                            # Print the text of the first link for verification
                            try:
                                link_text = links.first.text_content()
                                print(f"Link text: {link_text}")
                            except:
                                pass
                            break

                    if has_links:
                        print("✅ Found Funding Activities table with Fund Request ID links")
                        return table, fund_rows, row_count
                    else:
                        print("Table found but no links detected, retrying...")
                        time.sleep(5)
                        continue
                else:
                    print("No rows found in the table, retrying...")
                    time.sleep(5)
                    continue

            except Exception as e:
                print(f"Error finding table: {e}")
                time.sleep(5)

        # Try alternative approaches to find the table

        # As a last resort, try to find any table with links
        print("Last resort: Looking for any table with links...")
        tables = page.locator("table")
        for i in range(tables.count()):
            table = tables.nth(i)
            rows = table.locator("tbody tr")
            if rows.count() > 0:
                links = table.locator("a")
                if links.count() > 0:
                    print(f"Found a table with {rows.count()} rows and {links.count()} links")
                    return table, rows, rows.count()

        # If we still can't find anything, try to find any links on the page
        links = page.locator("a")
        if links.count() > 0:
            print(f"Found {links.count()} links on the page, will try these as a last resort")
            return page, links, links.count()

        raise Exception("Could not find Funding Activities table after multiple attempts")

    def _process_fund_requests(self, page, context):
        """Process fund requests from the table - first 3 pages"""
        # Get the Funding Activities table and rows
        _, fund_rows, row_count = self._find_funding_activities_table(page)

        # Process rows in the first page
        print(f"Processing first page of fund requests ({row_count} rows)")
        max_rows_to_process = row_count

        # Track total processed records across all pages
        total_processed = 0
        current_page = 1
        max_pages = 3  # Process first 3 pages

        # Process each row
        for i in range(max_rows_to_process):
            # Show progress
            total_processed += 1
            print(f"\n[Page {current_page}, Row {i+1}/{max_rows_to_process}] Processing record {total_processed}")
            try:
                print(f"➡️ Clicking Fund Request #{i+1}")
                row = fund_rows.nth(i)
                link = row.locator("a")

                # Always open in a new tab by using keyboard modifier
                print("Opening fund request in a new tab...")
                try:
                    # Use keyboard modifier to force opening in a new tab
                    with context.expect_page(timeout=5000) as new_page_info:
                        # Use Ctrl+Click (or Cmd+Click on Mac) to open in new tab
                        link.click(modifiers=["Control"])

                    # Get the new page
                    detail_page = new_page_info.value
                    print("New tab opened for fund details")

                    # Bring the new tab to front
                    detail_page.bring_to_front()
                except Exception as e:
                    print(f"Error opening in new tab: {e}")
                    print("Falling back to direct click...")

                    # Try again with a different approach
                    try:
                        with context.expect_page(timeout=5000) as new_page_info:
                            # Try right-click and then "Open in new tab"
                            link.click(button="right")
                            time.sleep(1)
                            page.keyboard.press("ArrowDown")
                            page.keyboard.press("Enter")
                        detail_page = new_page_info.value
                        detail_page.bring_to_front()
                    except:
                        # Last resort: just click and use the same page
                        print("All attempts to open in new tab failed, using same page")
                        link.click()
                        detail_page = page

                # Wait for the page to load
                detail_page.wait_for_load_state("networkidle", timeout=30000)
                time.sleep(3)  # Additional wait to ensure content is fully loaded

                # No screenshots needed

                # Verify we're on a fund details page (not looking for Funding Activities here)
                print("Verifying we're on a fund details page...")

                # Check for common elements that would be on a fund details page
                fund_details_indicators = [
                    "Active Status",
                    "Requested Cash Funding Amount",
                    "Fund Request History",
                    "Currency",
                ]

                found_indicator = False
                for indicator in fund_details_indicators:
                    indicator_locator = detail_page.locator(f"text={indicator}")
                    if indicator_locator.count() > 0:
                        print(f"Found fund details indicator: {indicator}")
                        found_indicator = True
                        break

                if not found_indicator:
                    print("Warning: Could not verify this is a fund details page")
                    # Continue anyway

                # First extract just the Fund Request ID and Active Status
                print("Extracting Fund Request ID and Active Status...")
                fund_id, active_status = self._extract_basic_info(detail_page)
                print(f"Fund Request ID: {fund_id}, Active Status: {active_status}")

                # Check if Active Status is Terminated or Active
                if active_status and (active_status.lower() == "terminated" or active_status.lower() == "active"):
                    print(f"⚠️ Skipping extraction for {fund_id} - Active Status is {active_status}")
                    # Create a minimal record with just the ID and status
                    data = {
                        "Fund Request ID": fund_id,
                        "Active Status": active_status,
                        "Requested Cash Funding Amount": None,
                        "Currency": None,
                        "Finance Approval Date": None,
                        "Skipped": True
                    }
                    self.records.append(data)
                else:
                    # Extract full data from the detail page
                    print(f"Extracting full data for {fund_id}...")
                    data = self._extract_fund_data(detail_page, fund_id, active_status)
                    if data:
                        self.records.append(data)
                        print(f"✅ Extracted data for Fund ID: {data['Fund Request ID']}")

                # Always close the tab and return to the dashboard
                print("Extraction complete, closing tab and returning to dashboard...")

                if detail_page != page:
                    # It was a new tab, close it and return to main page
                    try:
                        detail_page.close()
                        print("Closed detail tab")
                    except Exception as e:
                        print(f"Error closing tab: {e}")

                    # Make sure we're back on the dashboard
                    page.bring_to_front()
                    print("Returned to dashboard")
                else:
                    # It was the same page, go back to the dashboard
                    print("Was using same page, navigating back to dashboard")
                    page.goto("https://funding.awspartner.com/dashboard")

                # Wait for the dashboard to be ready again
                page.wait_for_load_state("networkidle", timeout=10000)
                time.sleep(2)

                # Wait for dashboard to load
                time.sleep(1)

            except Exception as e:
                print(f"Error at row {i+1}: {e}")
                # Continue with next record

        # Process additional pages
        while current_page < max_pages:
            print(f"\nMoving to page {current_page + 1}...")

            # Try to navigate to the next page
            next_page_found = self._navigate_to_next_page(page)

            if not next_page_found:
                print(f"Could not find next page button after page {current_page}")
                break

            current_page += 1

            # Wait for the page to load
            page.wait_for_load_state("networkidle", timeout=10000)
            time.sleep(3)

            # Get the new table rows
            _, fund_rows, row_count = self._find_funding_activities_table(page)

            print(f"Processing page {current_page} of fund requests ({row_count} rows)")
            max_rows_to_process = row_count

            # Process each row in this page
            for i in range(max_rows_to_process):
                # Show progress
                total_processed += 1
                print(f"\n[Page {current_page}, Row {i+1}/{max_rows_to_process}] Processing record {total_processed}")
                try:
                    print(f"➡️ Clicking Fund Request #{i+1}")
                    row = fund_rows.nth(i)
                    link = row.locator("a")

                    # Use the same code as above for processing each row
                    # Always open in a new tab by using keyboard modifier
                    print("Opening fund request in a new tab...")
                    try:
                        # Use keyboard modifier to force opening in a new tab
                        with context.expect_page(timeout=5000) as new_page_info:
                            # Use Ctrl+Click (or Cmd+Click on Mac) to open in new tab
                            link.click(modifiers=["Control"])

                        # Get the new page
                        detail_page = new_page_info.value
                        print("New tab opened for fund details")

                        # Bring the new tab to front
                        detail_page.bring_to_front()
                    except Exception as e:
                        print(f"Error opening in new tab: {e}")
                        print("Falling back to direct click...")

                        # Try again with a different approach
                        try:
                            with context.expect_page(timeout=5000) as new_page_info:
                                # Try right-click and then "Open in new tab"
                                link.click(button="right")
                                time.sleep(1)
                                page.keyboard.press("ArrowDown")
                                page.keyboard.press("Enter")
                            detail_page = new_page_info.value
                            detail_page.bring_to_front()
                        except:
                            # Last resort: just click and use the same page
                            print("All attempts to open in new tab failed, using same page")
                            link.click()
                            detail_page = page

                    # Wait for the page to load
                    detail_page.wait_for_load_state("networkidle", timeout=30000)
                    time.sleep(3)  # Additional wait to ensure content is fully loaded

                    # No screenshots needed

                    # Verify we're on a fund details page (not looking for Funding Activities here)
                    print("Verifying we're on a fund details page...")

                    # Check for common elements that would be on a fund details page
                    fund_details_indicators = [
                        "Active Status",
                        "Requested Cash Funding Amount",
                        "Fund Request History",
                        "Currency",
                    ]

                    found_indicator = False
                    for indicator in fund_details_indicators:
                        indicator_locator = detail_page.locator(f"text={indicator}")
                        if indicator_locator.count() > 0:
                            print(f"Found fund details indicator: {indicator}")
                            found_indicator = True
                            break

                    if not found_indicator:
                        print("Warning: Could not verify this is a fund details page")
                        # Continue anyway

                    # First extract just the Fund Request ID and Active Status
                    print("Extracting Fund Request ID and Active Status...")
                    fund_id, active_status = self._extract_basic_info(detail_page)
                    print(f"Fund Request ID: {fund_id}, Active Status: {active_status}")

                    # Check if Active Status is Terminated or Active
                    if active_status and (active_status.lower() == "terminated" or active_status.lower() == "active"):
                        print(f"⚠️ Skipping extraction for {fund_id} - Active Status is {active_status}")
                        # Create a minimal record with just the ID and status
                        data = {
                            "Fund Request ID": fund_id,
                            "Active Status": active_status,
                            "Requested Cash Funding Amount": None,
                            "Currency": None,
                            "Finance Approval Date": None,
                            "Skipped": True
                        }
                        self.records.append(data)
                    else:
                        # Extract full data from the detail page
                        print(f"Extracting full data for {fund_id}...")
                        data = self._extract_fund_data(detail_page, fund_id, active_status)
                        if data:
                            self.records.append(data)
                            print(f"✅ Extracted data for Fund ID: {data['Fund Request ID']}")

                    # Always close the tab and return to the dashboard
                    print("Extraction complete, closing tab and returning to dashboard...")

                    if detail_page != page:
                        # It was a new tab, close it and return to main page
                        try:
                            detail_page.close()
                            print("Closed detail tab")
                        except Exception as e:
                            print(f"Error closing tab: {e}")

                        # Make sure we're back on the dashboard
                        page.bring_to_front()
                        print("Returned to dashboard")
                    else:
                        # It was the same page, go back to the dashboard
                        print("Was using same page, navigating back to dashboard")
                        page.goto("https://funding.awspartner.com/dashboard")

                    # Wait for the dashboard to be ready again
                    page.wait_for_load_state("networkidle", timeout=10000)
                    time.sleep(2)

                    # Wait for dashboard to load
                    time.sleep(1)

                except Exception as e:
                    print(f"Error at row {i+1} on page {current_page}: {e}")
                    # Continue with next record

        # Print summary of processed records
        skipped_count = sum(1 for record in self.records if record.get('Skipped', False))
        processed_count = len(self.records) - skipped_count

        print(f"\nCompleted processing {len(self.records)} records across {current_page} pages")
        print(f"Skipped: {skipped_count}, Fully processed: {processed_count}")

    def _extract_basic_info(self, page):
        """Extract just the Fund Request ID and Active Status from a fund detail page"""
        try:
            # Wait for content to load
            page.wait_for_timeout(2000)

            # Get the page content and parse with BeautifulSoup
            soup = BeautifulSoup(page.content(), "html.parser")

            # Extract Fund Request ID from the details page
            fund_id = None

            # Look for the Fund Request ID label and extract the value
            print("Looking for 'Fund Request ID' label in the details page...")

            # Method 1: Try to find it by looking for a label with "Fund Request ID"
            try:
                # Look for the Fund Request ID label
                id_label = soup.find(string=lambda text: text and "Fund Request ID" in text)
                if id_label:
                    print("Found 'Fund Request ID' label")
                    # Try to get the next element which should contain the ID
                    if id_label.find_next():
                        fund_id = id_label.find_next().get_text(strip=True)
                        print(f"Found Fund Request ID from label: {fund_id}")
                    # If that doesn't work, try to find a parent element and then look for the value
                    elif id_label.parent:
                        # Try to find siblings or nearby elements
                        siblings = id_label.parent.find_next_siblings()
                        for sibling in siblings:
                            if sibling.text and "FR-" in sibling.text:
                                fund_id = sibling.text.strip()
                                print(f"Found Fund Request ID from sibling: {fund_id}")
                                break
            except Exception as e:
                print(f"Error finding Fund Request ID by label: {e}")

            # Method 2: Try to find it by looking for a specific element structure
            if not fund_id:
                try:
                    # Based on your screenshot, try to find elements with class patterns
                    # that might contain the Fund Request ID
                    id_containers = soup.find_all('div', class_=lambda c: c and 'awsui_root' in c)
                    for container in id_containers:
                        text = container.text.strip()
                        if 'FR-' in text:
                            # Use regex to extract the ID pattern - be very specific to match the format in the screenshot
                            import re
                            # Look for FR-MDF-COMBO-<alphanumeric>-<alphanumeric> pattern
                            # Match the exact format shown in the screenshot: FR-MDF-COMBO-c57156f351a8-0f7561ae21f6
                            id_match = re.search(r'\b(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)\b', text)
                            # If that doesn't work, try without word boundary
                            if not id_match:
                                id_match = re.search(r'(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)', text)
                            if id_match:
                                fund_id = id_match.group(1)
                                print(f"Found Fund Request ID from container: {fund_id}")
                                break
                except Exception as e:
                    print(f"Error finding Fund Request ID by container: {e}")

            # Method 3: Try to find it by looking for any element containing the ID pattern
            if not fund_id:
                try:
                    # Look for any element containing the FR- pattern
                    fr_elements = soup.find_all(string=lambda text: text and "FR-" in text)
                    for elem in fr_elements:
                        text = elem.strip()
                        import re
                        # Look for FR-MDF-COMBO-<alphanumeric>-<alphanumeric> pattern
                        # Match the exact format shown in the screenshot: FR-MDF-COMBO-c57156f351a8-0f7561ae21f6
                        id_match = re.search(r'\b(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)\b', text)
                        # If that doesn't work, try without word boundary
                        if not id_match:
                            id_match = re.search(r'(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)', text)
                        if id_match:
                            fund_id = id_match.group(1)
                            print(f"Found Fund Request ID from text: {fund_id}")
                            break
                except Exception as e:
                    print(f"Error finding Fund Request ID by text pattern: {e}")

            # Method 4: Look for the specific structure shown in the screenshot
            if not fund_id:
                try:
                    # Based on your screenshot, the Fund Request ID is in a red box
                    # Try to find elements that might be in that structure
                    red_boxes = soup.find_all('div', style=lambda s: s and 'border' in s.lower())
                    for box in red_boxes:
                        if box.text and 'Fund Request ID' in box.text:
                            # Extract the ID using regex
                            import re
                            # Look for FR-MDF-COMBO-<alphanumeric>-<alphanumeric> pattern
                            # Match the exact format shown in the screenshot: FR-MDF-COMBO-c57156f351a8-0f7561ae21f6
                            id_match = re.search(r'\b(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)\b', box.text)
                            # If that doesn't work, try without word boundary
                            if not id_match:
                                id_match = re.search(r'(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)', box.text)
                            if id_match:
                                fund_id = id_match.group(1)
                                print(f"Found Fund Request ID from red box: {fund_id}")
                                break
                except Exception as e:
                    print(f"Error finding Fund Request ID in red box: {e}")

            # Method 4b: Try to find the exact structure from your screenshot
            if not fund_id:
                try:
                    # Look for the exact structure from your screenshot
                    # The Fund Request ID is in a separate div after the label
                    fund_id_label = soup.find(string="Fund Request ID")
                    if fund_id_label and fund_id_label.parent:
                        # Try to find the next div which should contain just the ID
                        next_div = fund_id_label.parent.find_next_sibling("div")
                        if next_div:
                            id_text = next_div.text.strip()
                            print(f"Found text in next div: {id_text}")
                            # Make sure it matches the expected format
                            if id_text.startswith("FR-"):
                                fund_id = id_text
                                print(f"Found Fund Request ID from exact structure: {fund_id}")
                except Exception as e:
                    print(f"Error finding Fund Request ID from exact structure: {e}")

            # Method 5: Use Playwright to find elements
            if not fund_id:
                try:
                    # Try to find the Fund Request ID

                    # Try to find the Fund Request ID using Playwright locators
                    id_elements = page.locator("text=Fund Request ID")
                    if id_elements.count() > 0:
                        print(f"Found {id_elements.count()} elements with 'Fund Request ID' text")
                        # Try to get the parent element and then find the value
                        parent = id_elements.first.locator("xpath=..")
                        if parent.count() > 0:
                            # Try to get the text of the parent and extract the ID
                            parent_text = parent.text_content()
                            import re
                            # Look for FR-MDF-COMBO-<alphanumeric>-<alphanumeric> pattern
                            # Match the exact format shown in the screenshot: FR-MDF-COMBO-c57156f351a8-0f7561ae21f6
                            id_match = re.search(r'\b(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)\b', parent_text)
                            # If that doesn't work, try without word boundary
                            if not id_match:
                                id_match = re.search(r'(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)', parent_text)
                            if id_match:
                                fund_id = id_match.group(1)
                                print(f"Found Fund Request ID from parent: {fund_id}")
                except Exception as e:
                    print(f"Error finding Fund Request ID using Playwright: {e}")

            # Method 6: Use Playwright's CSS selectors to find the exact element structure
            if not fund_id:
                try:
                    # Based on your screenshot, try to find the exact element structure
                    # First try to find the red box
                    red_box = page.locator("div[style*='border']")
                    if red_box.count() > 0:
                        print(f"Found {red_box.count()} elements with border style")
                        # Try each one
                        for i in range(red_box.count()):
                            box = red_box.nth(i)
                            box_text = box.text_content()
                            if "Fund Request ID" in box_text:
                                print(f"Found box with Fund Request ID text: {box_text}")
                                # Extract just the ID part
                                lines = box_text.split("\n")
                                for line in lines:
                                    line = line.strip()
                                    if line.startswith("FR-"):
                                        # Clean up the ID to ensure it matches the expected format
                                        import re
                                        id_match = re.search(r'(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)', line)
                                        if id_match:
                                            fund_id = id_match.group(1)
                                        else:
                                            fund_id = line
                                        print(f"Found Fund Request ID from box: {fund_id}")
                                        break
                            if fund_id:
                                break
                except Exception as e:
                    print(f"Error finding Fund Request ID using CSS selectors: {e}")

            # Method 7: Use the exact structure from the screenshot
            if not fund_id:
                try:
                    # Based on the screenshot, the Fund Request ID is in a specific structure
                    # Try to find the exact element structure shown in the screenshot
                    # Look for the Fund Request ID label and then the next element with the ID
                    fund_id_label = page.locator("text='Fund Request Id'")
                    if fund_id_label.count() > 0:
                        print("Found 'Fund Request Id' label")
                        # Try to find the ID element

                        # Try to find the ID element - it's likely in a nearby element
                        # First try the next sibling
                        id_element = fund_id_label.locator("xpath=following-sibling::*[1]")
                        if id_element.count() > 0:
                            id_text = id_element.text_content().strip()
                            if id_text.startswith("FR-"):
                                fund_id = id_text
                                print(f"Found Fund Request ID from exact structure: {fund_id}")

                        # If that didn't work, try the parent's next sibling
                        if not fund_id:
                            parent = fund_id_label.locator("xpath=..")
                            if parent.count() > 0:
                                next_sibling = parent.locator("xpath=following-sibling::*[1]")
                                if next_sibling.count() > 0:
                                    id_text = next_sibling.text_content().strip()
                                    if id_text.startswith("FR-"):
                                        fund_id = id_text
                                        print(f"Found Fund Request ID from parent's next sibling: {fund_id}")
                except Exception as e:
                    print(f"Error finding Fund Request ID using exact structure: {e}")

            # Method 8: Use the exact structure from the screenshot with case-insensitive matching
            if not fund_id:
                try:
                    # Try with case-insensitive matching for 'Fund Request Id'
                    # The screenshot shows 'Fund Request Id' but it might be 'Fund Request ID' in some cases
                    fund_id_elements = page.locator("xpath=//*[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'fund request id')]")
                    if fund_id_elements.count() > 0:
                        print(f"Found {fund_id_elements.count()} elements with 'fund request id' text (case insensitive)")

                        # Try each element
                        for i in range(fund_id_elements.count()):
                            element = fund_id_elements.nth(i)
                            # Try to find the ID in the next element or nearby
                            parent = element.locator("xpath=..")
                            if parent.count() > 0:
                                # Try to find any element within the parent that contains FR-
                                fr_elements = parent.locator("xpath=.//*[contains(text(), 'FR-')]")
                                if fr_elements.count() > 0:
                                    for j in range(fr_elements.count()):
                                        fr_element = fr_elements.nth(j)
                                        id_text = fr_element.text_content().strip()
                                        # Extract just the FR- part using regex
                                        import re
                                        id_match = re.search(r'(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)', id_text)
                                        if id_match:
                                            fund_id = id_match.group(1)
                                            print(f"Found Fund Request ID from case-insensitive search: {fund_id}")
                                            break
                            if fund_id:
                                break
                except Exception as e:
                    print(f"Error finding Fund Request ID using case-insensitive search: {e}")

            # If still not found, use a timestamp as last resort
            if not fund_id:
                import datetime
                fund_id = f"UNKNOWN-{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}"
                print(f"Could not find Fund Request ID, using generated ID: {fund_id}")
            else:
                # Clean up the Fund Request ID to remove any unwanted text
                if fund_id:
                    # Remove any text after the expected pattern
                    import re
                    # Match the exact format shown in the screenshot: FR-MDF-COMBO-c57156f351a8-0f7561ae21f6
                    clean_match = re.match(r'(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)', fund_id)
                    if clean_match:
                        clean_id = clean_match.group(1)
                        if clean_id != fund_id:
                            print(f"Cleaned up Fund Request ID: {fund_id} -> {clean_id}")
                            fund_id = clean_id

                print(f"Successfully extracted Fund Request ID: {fund_id}")

            # Continue with extraction

            # === Extract Active Status ===
            active_status = None
            try:
                # Try to find by exact text
                status_tag = soup.find(string="Active Status")
                if status_tag:
                    active_status = status_tag.find_next().get_text(strip=True)
                    print(f"Found Active Status: {active_status}")
                else:
                    # Try to find by containing text
                    status_elements = [elem for elem in soup.find_all() if "status" in elem.text.lower()]
                    for elem in status_elements:
                        if elem.find_next():
                            active_status = elem.find_next().get_text(strip=True)
                            print(f"Found Active Status: {active_status}")
                            break
            except Exception as e:
                print(f"Error extracting Active Status: {e}")

            return fund_id, active_status

        except Exception as e:
            print(f"Error extracting basic info: {e}")
            return None, None

    def _extract_fund_data(self, page, fund_id=None, active_status=None):
        """Extract data from a fund detail page"""
        try:
            # Wait for content to load
            page.wait_for_timeout(3000)

            # Get the page content and parse with BeautifulSoup
            soup = BeautifulSoup(page.content(), "html.parser")

            # Use the provided Fund Request ID or extract it if not provided
            if not fund_id:
                # Call the _extract_basic_info method to get the Fund Request ID
                print("Fund ID not provided, extracting from page...")
                fund_id, _ = self._extract_basic_info(page)

                if not fund_id:
                    # Last resort: use a timestamp as ID
                    import datetime
                    fund_id = f"UNKNOWN-{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}"

                print(f"Processing Fund ID: {fund_id}")

            # Use the provided Active Status or extract it if not provided
            if not active_status:
                try:
                    # Try to find by exact text
                    status_tag = soup.find(string="Active Status")
                    if status_tag:
                        active_status = status_tag.find_next().get_text(strip=True)
                        print(f"Active Status: {active_status}")
                    else:
                        # Try to find by containing text
                        status_elements = [elem for elem in soup.find_all() if "status" in elem.text.lower()]
                        for elem in status_elements:
                            if elem.find_next():
                                active_status = elem.find_next().get_text(strip=True)
                                print(f"Found Active Status: {active_status}")
                                break
                except Exception as e:
                    print(f"Error extracting Active Status: {e}")

            # === Extract Amount ===
            amount = None
            amount_labels = [
                "Requested Cash Funding Amount (Local Currency)",
                "Requested MDF Cash Funding (Local Currency)",
                "Claim Amount (Local Currency)",
                "Requested Cash Funding Amount",
                "Cash Funding Amount",
                "Funding Amount",
            ]

            for label in amount_labels:
                try:
                    # Try exact match first
                    amount_tag = soup.find(string=label)
                    if amount_tag and amount_tag.find_next():
                        amount = amount_tag.find_next().get_text(strip=True)
                        print(f"Found amount using label: '{label}' = {amount}")
                        break

                    # Try partial match
                    amount_elements = [elem for elem in soup.find_all() if label.lower() in elem.text.lower()]
                    for elem in amount_elements:
                        if elem.find_next():
                            amount = elem.find_next().get_text(strip=True)
                            print(f"Found amount using partial match: '{label}' = {amount}")
                            break
                    if amount:
                        break
                except Exception as e:
                    print(f"Error extracting amount with label '{label}': {e}")

            # === Extract Currency ===
            currency = None
            currency_labels = [
                "Currency",
                "Local Currency",
                "Currency Code",
            ]

            for label in currency_labels:
                try:
                    # Try exact match first
                    currency_tag = soup.find(string=label)
                    if currency_tag and currency_tag.find_next():
                        currency = currency_tag.find_next().get_text(strip=True)
                        print(f"Found currency using label: '{label}' = {currency}")
                        break

                    # Try partial match
                    currency_elements = [elem for elem in soup.find_all() if label.lower() in elem.text.lower()]
                    for elem in currency_elements:
                        if elem.find_next():
                            currency = elem.find_next().get_text(strip=True)
                            print(f"Found currency using partial match: '{label}' = {currency}")
                            break
                    if currency:
                        break
                except Exception as e:
                    print(f"Error extracting currency with label '{label}': {e}")

            # === Look for Fund Request History section and find Finance Approval date ===
            print("Looking for Fund Request History section...")
            history_selectors = [
                "text=Fund Request History",
                "h2:has-text('Fund Request History')",
                "div:has-text('Fund Request History')",
            ]

            for selector in history_selectors:
                try:
                    history_locator = page.locator(selector)
                    if history_locator.count() > 0:
                        print(f"Found Fund Request History section with selector: {selector}")
                        # Found the history section
                        break
                except:
                    continue

            # Find Finance Approval date
            approval_date = self._find_finance_approval_date(page)

            return {
                "Fund Request ID": fund_id,
                "Active Status": active_status,
                "Requested Cash Funding Amount": amount,
                "Currency": currency,
                "Finance Approval Date": approval_date,
                "Skipped": False
            }

        except Exception as e:
            print(f"Error extracting fund data: {e}")
            return None

    def _find_finance_approval_date(self, page):
        """Find the Finance Approval date in the Fund Request History section"""
        approval_date = None
        page_num = 1
        max_pages = 10  # Safety limit to prevent infinite loops

        print("Looking for Finance Approval date in Fund Request History...")

        # Look for Finance Approval date

        while page_num <= max_pages:
            print(f"Checking page {page_num} for Finance Approval...")

            # Get the current page content
            soup = BeautifulSoup(page.content(), "html.parser")

            # Look for the table rows
            rows = soup.select("table tbody tr")
            print(f"Found {len(rows)} rows in the table")

            # Check each row for Finance Approval
            for i, row in enumerate(rows):
                tds = row.find_all("td")
                if len(tds) < 3:  # Skip rows with insufficient columns
                    continue

                # Based on the screenshot, we need to extract:
                # Date column (index 1)
                # Action column (index 2) - looking for "ChangedStage"
                # Stage column (index 5 or 6) - looking for "finance_approval"

                # Extract values from columns
                date_text = tds[1].text.strip() if len(tds) > 1 else "N/A"
                action_text = tds[2].text.strip() if len(tds) > 2 else "N/A"

                # Find the Stage column - based on the screenshot, it should be the last column
                # But we'll check multiple positions to be safe
                stage_text = ""

                # First try to find the column by header text
                stage_column_idx = None
                headers = soup.select("table thead th")
                for i, header in enumerate(headers):
                    header_text = header.text.strip().lower()
                    if "stage" in header_text:
                        stage_column_idx = i
                        print(f"Found Stage column at index {stage_column_idx}")
                        break

                # If we found the Stage column by header, use that index
                if stage_column_idx is not None and len(tds) > stage_column_idx:
                    stage_text = tds[stage_column_idx].text.strip()
                else:
                    # Otherwise try some likely positions
                    stage_column_candidates = [5, 6, 7, -1]  # Possible indices for Stage column, including last column

                    for col_idx in stage_column_candidates:
                        try:
                            if col_idx == -1:  # Last column
                                col_text = tds[-1].text.strip()
                            elif len(tds) > col_idx:
                                col_text = tds[col_idx].text.strip()
                            else:
                                continue

                            if col_text:
                                stage_text = col_text
                                # If we found finance_approval, we can stop looking
                                if "finance_approval" in col_text.lower() or "finance" in col_text.lower():
                                    break
                        except:
                            continue

                # Print row content for debugging
                print(f"Row {i+1}: Date={date_text}, Action={action_text}, Stage={stage_text}")

                # Check if this is the finance approval row
                # Looking for Action="ChangedStage" and Stage="finance_approval"
                # Based on the screenshot, we need to find a row where:
                # 1. Action column shows "ChangedStage"
                # 2. Stage column shows "finance_approval"
                if ("changedstage" in action_text.lower() and
                    ("finance_approval" in stage_text.lower() or "finance" in stage_text.lower())):
                    # Found the finance approval row
                    print(f"✅ Found row with ChangedStage and finance_approval")
                    approval_date = date_text
                    print(f"✅ Found Finance Approval date: {approval_date}")
                    return approval_date

            # If we didn't find it, try to go to the next page
            try:
                # First try the angle-right icon/arrow as shown in the screenshot
                print("Looking for pagination arrow...")
                arrow_locators = [
                    # Try by SVG icon name
                    page.locator("span.awsui_name-angle-right"),
                    # Try by the button containing the arrow
                    page.locator("button:has(span.awsui_name-angle-right)"),
                    # Try by the '>' character
                    page.locator("span:has-text('>')"),
                    # Try by the pagination number + 1
                    page.locator(f"[aria-label='{page_num + 1}']"),
                    # Try the Next button
                    page.locator("button:has-text('Next')"),
                ]

                next_clicked = False
                for i, locator in enumerate(arrow_locators):
                    if locator.count() > 0 and locator.is_visible() and locator.is_enabled():
                        print(f"Found pagination control (method {i+1}), clicking...")
                        locator.click()
                        time.sleep(2)  # Wait for page to update
                        next_clicked = True
                        page_num += 1
                        break

                if not next_clicked:
                    # Try one more approach - look for the next page number directly
                    next_page = page.locator(f"text={page_num + 1}").first
                    if next_page.count() > 0 and next_page.is_visible():
                        print(f"Clicking page number {page_num + 1}...")
                        next_page.click()
                        time.sleep(2)
                        page_num += 1
                    else:
                        print("No more pagination controls found, reached last page")
                        break

            except Exception as e:
                print(f"Error navigating pagination: {e}")
                # Continue to next attempt
                break

        print("Finance Approval date not found after checking all pages")
        return None

    def _save_to_excel(self, timestamp=None):
        """Save the extracted data to an Excel file"""
        if not self.records:
            print("No data to save")
            return

        # Generate timestamp if not provided
        if timestamp is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create DataFrame from records
        df = pd.DataFrame(self.records)

        # Clean up any Fund Request IDs that might still have unwanted text
        if 'Fund Request ID' in df.columns:
            # Function to clean Fund Request IDs
            def clean_fund_id(fund_id):
                if isinstance(fund_id, str) and fund_id.startswith('FR-'):
                    import re
                    # Match the exact format shown in the screenshot: FR-MDF-COMBO-c57156f351a8-0f7561ae21f6
                    clean_match = re.match(r'(FR-MDF-COMBO-[a-z0-9]+-[a-z0-9]+)', fund_id)
                    if clean_match:
                        return clean_match.group(1)
                return fund_id

            # Apply the cleaning function to the Fund Request ID column
            df['Fund Request ID'] = df['Fund Request ID'].apply(clean_fund_id)
            print("Cleaned up all Fund Request IDs in the DataFrame")

        # Ensure Fund Request ID is the first column
        if 'Fund Request ID' in df.columns:
            # Reorder columns to put Fund Request ID first
            cols = ['Fund Request ID']
            cols.extend([col for col in df.columns if col != 'Fund Request ID'])
            df = df[cols]

            # Print summary of extracted data
            print(f"\nTotal records: {len(df)}")

        # Save to Excel with timestamp to avoid overwriting previous files
        excel_filename = f"fund_requests_data_{timestamp}.xlsx"
        df.to_excel(excel_filename, index=False)
        print(f"\nData saved to {excel_filename}")

        # Also save a CSV backup
        csv_filename = f"fund_requests_data_{timestamp}.csv"
        df.to_csv(csv_filename, index=False)
        print(f"Backup saved to {csv_filename}")


# Run the extractor
if __name__ == "__main__":
    print("Starting Fund Request Extractor")

    # Check if aws_session.json exists
    if not os.path.exists("aws_session.json"):
        print("aws_session.json not found. Please make sure you have a valid session file.")
        exit(1)

    extractor = FundRequestExtractor(headless=False)
    extractor.run()
