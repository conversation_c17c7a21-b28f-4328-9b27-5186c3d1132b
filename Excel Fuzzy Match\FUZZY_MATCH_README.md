# Fuzzy Matching Tool for AE Name Assignment

This tool performs fuzzy matching between two Excel files to assign Account Executive (AE) names from one file to another.

## Purpose

The script compares company names between two Excel files:
1. A target file containing a list of companies that need AE names assigned
2. An account list file containing company names and their associated AE/Owner names

Using fuzzy matching, the script finds the best matches between company names and assigns the corresponding AE names to the target file.

## Requirements

- Python 3.6 or higher
- Required packages:
  - pandas
  - rapidfuzz
  - openpyxl

## Installation

1. Make sure you have Python installed
2. Install required packages:
   ```
   pip install pandas rapidfuzz openpyxl
   ```

## Usage

1. Run the script:
   ```
   python fuzzy_match_ae.py
   ```

2. The script will:
   - Load the target file and account list file
   - Prompt for column names if they don't match the expected names
   - Ask for a match threshold (default: 80)
   - Perform fuzzy matching
   - Update the target file with AE names
   - Save the results

## Default File Paths

- Target file: `C:\Users\<USER>\OneDrive - Adastra, s.r.o\Desktop\Fuzzy Match Target - ENT GFC Propensity List OCT.xlsx`
- Account list file: `C:\Users\<USER>\Downloads\JT - CA & US Accounts 2025_05_02 2-44-05 PM.xlsx`
- Output file: `C:\Users\<USER>\OneDrive - Adastra, s.r.o\Desktop\Fuzzy Match Target - ENT GFC Propensity List OCT - Updated.xlsx`

## Expected Column Names

- Target file:
  - Company name column: `gtm_customer_name`
  - AE name column: `Adastra AE Name`
- Account list file:
  - Company name column: `Account Name`
  - AE/Owner name column: `Owner`

## Output

The script creates:
1. An updated version of the target file with AE names filled in
2. A backup of the original target file
3. A separate Excel file with matching results

## Customization

To use different files or column names, you can modify the script or follow the prompts when running the script.

## Troubleshooting

If the script fails to find the expected columns, it will prompt you to enter the correct column names. You can view the available columns in the console output.

If you encounter any issues with file paths, make sure the files exist at the specified locations.

## Match Threshold

The match threshold (0-100) determines how strict the matching should be:
- Higher values (e.g., 90) require closer matches
- Lower values (e.g., 70) allow more lenient matching
- The default is 80, which provides a good balance

## Contact

For questions or issues, please contact the developer.
