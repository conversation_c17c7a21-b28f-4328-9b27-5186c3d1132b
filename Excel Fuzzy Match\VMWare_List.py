# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np

# 这里改为你本机的实际路径
OPP_FILE = r"C:\Users\<USER>\Downloads\JT-All Opportunities 2025_09_22 2-54-41 PM.xlsx"
VMWARE_FILE = r"C:\Users\<USER>\OneDrive - Adastra, s.r.o\Desktop\Adastra - VMWARE List - 1.xlsx"
OUT_FILE = r"C:\Users\<USER>\OneDrive - Adastra, s.r.o\Desktop\Adastra - VMWARE List - 1__matched.xlsx"

# ---- 尝试使用 rapidfuzz（更稳健的模糊匹配）；不可用则回退至 difflib ----
try:
    from rapidfuzz import fuzz, process
    HAVE_RAPIDFUZZ = True
except Exception:
    import difflib
    HAVE_RAPIDFUZZ = False

def norm(s):
    if pd.isna(s):
        return ""
    return str(s).strip().lower()

def find_col(candidates, df):
    cols = list(df.columns)
    for c in candidates:
        for col in cols:
            if c.lower() == str(col).strip().lower():
                return col
    for c in candidates:
        for col in cols:
            if c.lower() in str(col).strip().lower():
                return col
    if not cols:
        return None
    if HAVE_RAPIDFUZZ:
        scores = [(col, fuzz.token_set_ratio(candidates[0], str(col))) for col in cols]
        return max(scores, key=lambda x: x[1])[0]
    else:
        best = difflib.get_close_matches(candidates[0], [str(c) for c in cols], n=1, cutoff=0)
        return best[0] if best else None

def best_match(name, choices, threshold=80):
    if not name:
        return None, 0
    if HAVE_RAPIDFUZZ:
        m = process.extractOne(name, choices, scorer=fuzz.token_set_ratio, score_cutoff=threshold)
        if m:
            return m[0], m[1]
        return None, 0
    else:
        scores = [(cand, int(100 * difflib.SequenceMatcher(None, name, cand).ratio())) for cand in choices]
        if not scores:
            return None, 0
        best = max(scores, key=lambda x: x[1])
        return best if best[1] >= threshold else (None, 0)

def first_non_lost(group):
    non_lost = group[~group["Status"].astype(str).str.lower().str.contains("lost", na=False)]
    return (non_lost.iloc[0] if len(non_lost) else group.iloc[0])

def main():
    opp_df = pd.read_excel(OPP_FILE, sheet_name="JT-All Opportunities")
    vmware_df = pd.read_excel(VMWARE_FILE, sheet_name="Prospect Customer (Exp >=2025)")

    opp_account_col = find_col(["Account"], opp_df) or "Account"
    opp_name_col    = find_col(["Opportunity Name","Opp Name"], opp_df) or "Opportunity Name"
    opp_owner_col   = find_col(["Owner","Opportunity Owner"], opp_df) or "Owner"
    opp_status_col  = find_col(["Status","Stage"], opp_df) or "Status"
    vm_customer_col = find_col(["Customer Name","Customer"], vmware_df) or "Customer Name"

    vm = vmware_df.copy()
    vm["_Customer_norm"] = vm[vm_customer_col].apply(norm)
    valid_idx = vm.index[vm["_Customer_norm"].str.len() > 0]

    opp = opp_df[[opp_account_col, opp_name_col, opp_owner_col, opp_status_col]].copy()
    opp.columns = ["Account","Opportunity Name","Owner","Status"]
    opp["Account_norm"] = opp["Account"].apply(norm)

    opp_nonlost = opp.groupby("Account_norm", as_index=False).apply(first_non_lost).reset_index(drop=True)
    account_names = opp_nonlost["Account"].fillna("").astype(str).tolist()

    for col in ["Matched Name","Opportunity Name","Owner","Status","_Match Score"]:
        if col not in vm.columns:
            vm[col] = np.nan

    for idx in valid_idx:
        cust_norm = vm.at[idx, "_Customer_norm"]
        matched, score = best_match(cust_norm, account_names, threshold=80)
        vm.at[idx, "_Match Score"] = score
        if matched:
            vm.at[idx, "Matched Name"] = matched
            key = norm(matched)
            row = opp_nonlost.loc[opp_nonlost["Account_norm"] == key]
            if not row.empty:
                status_val = str(row.iloc[0]["Status"]) if pd.notna(row.iloc[0]["Status"]) else ""
                if "lost" not in status_val.strip().lower():
                    vm.at[idx, "Opportunity Name"] = row.iloc[0]["Opportunity Name"]
                    vm.at[idx, "Owner"] = row.iloc[0]["Owner"]
                    vm.at[idx, "Status"] = row.iloc[0]["Status"]

    vm = vm.drop(columns=["_Customer_norm"])

    from openpyxl import load_workbook
    from openpyxl.utils.dataframe import dataframe_to_rows

    wb = load_workbook(VMWARE_FILE)
    target_sheet = "Prospect Customer (Exp >=2025)"
    if target_sheet in wb.sheetnames:
        del wb[target_sheet]
    ws = wb.create_sheet(title=target_sheet, index=0)
    for r in dataframe_to_rows(vm, index=False, header=True):
        ws.append(r)

    wb.save(OUT_FILE)
    print(f"完成！输出文件位置: {OUT_FILE}")

if __name__ == "__main__":
    main()
