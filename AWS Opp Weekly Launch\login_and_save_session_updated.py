import os
import time
import json
from playwright.sync_api import sync_playwright

# Main function to handle login and save session
def login_and_save_session():
    print("Starting login process...")

    with sync_playwright() as p:
        # Launch browser with larger viewport to ensure all elements are visible
        browser = p.chromium.launch(headless=False)  # Set headless=False so you can log in manually
        context = browser.new_context(viewport={"width": 1280, "height": 800})
        page = context.new_page()

        # 1. Go to the AWS Partner Central login page
        print("🌐 Opening AWS Partner Central login page...")
        try:
            page.goto("https://partnercentral.awspartner.com/", timeout=60000)  # 60 second timeout
        except Exception as e:
            print(f"Error navigating to login page: {e}")
            print("Trying again with a longer timeout...")
            page.goto("https://partnercentral.awspartner.com/", timeout=120000)  # 2 minute timeout

        # 2. Manually log in
        print("🔐 Please log in manually in the opened browser window...")
        print("Take your time to complete the login process...")

        # Wait for user to confirm they've completed login
        print("\nPress Enter when you have completed the login process and can see the AWS Partner Central dashboard...")
        input()

        # 3. Take a screenshot to verify
        print("Taking a screenshot of the current page...")
        page.screenshot(path="login_complete.png")
        print("Screenshot saved as login_complete.png")

        # Ask user to confirm if login is successful
        print("\nDo you see the AWS Partner Central dashboard? (y/n): ")
        user_input = input().strip().lower()

        login_successful = (user_input == 'y')

        if login_successful:
            print("✅ Login confirmed by user. Proceeding to save session.")
        else:
            print("Login not confirmed. Please try again.")
            browser.close()
            return False

        # Additional wait to ensure page is fully loaded
        print("Waiting additional time for page to fully load...")
        time.sleep(5)

        # 4. Save session to file
        print("💾 Saving session to aws_session.json")
        try:
            # First, print all cookies for debugging
            cookies = context.cookies()
            print(f"Found {len(cookies)} cookies before saving")

            # Save the session state
            context.storage_state(path="aws_session.json")
            print("Session state saved successfully.")
        except Exception as e:
            print(f"Error saving session state: {e}")
            browser.close()
            return False

        # 5. Verify the session file was created and has content
        if os.path.exists("aws_session.json"):
            try:
                with open("aws_session.json", "r") as f:
                    session_data = json.load(f)

                # Check if the session has cookies
                if "cookies" in session_data and len(session_data["cookies"]) > 0:
                    print(f"✅ Session saved successfully with {len(session_data['cookies'])} cookies.")
                else:
                    print("⚠️ Session file created but no cookies found. Login might not have been successful.")
                    browser.close()
                    return False
            except Exception as e:
                print(f"⚠️ Error reading session file: {e}")
                browser.close()
                return False
        else:
            print("❌ Session file was not created. Something went wrong.")
            browser.close()
            return False

        print("Closing browser...")
        browser.close()
        return True

# Run the function if this script is executed directly
if __name__ == "__main__":
    success = login_and_save_session()
    if success:
        print("\n✅ Login and session save completed successfully!")
        print("You can now run your AWS Partner Central scripts.")
    else:
        print("\n❌ Login and session save failed.")
        print("Please try again.")
