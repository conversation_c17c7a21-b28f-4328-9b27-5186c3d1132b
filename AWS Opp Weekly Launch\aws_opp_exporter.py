import os
import time
import datetime
import pandas as pd
import shutil
from pathlib import Path
import glob
import logging
import sys
from playwright.sync_api import sync_playwright

# Configure logging to create log file in current folder
log_filename = os.path.join(os.path.dirname(__file__), 'aws_opp_export_log.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler(sys.stdout)
    ]
)

print(f"Log file will be created at: {log_filename}")

class AWSOpportunityExporter:
    def __init__(self, headless=False, storage_state="aws_session.json"):
        """Initialize the AWS Opportunity Exporter.

        Args:
            headless (bool): Whether to run the browser in headless mode
            storage_state (str): Path to the saved session state file
        """
        self.headless = headless
        self.storage_state = storage_state
        # Use standard Windows Downloads folder path
        self.download_folder = os.path.join(os.path.expanduser("~"), "Downloads")
        self.destination_folder = os.path.expanduser("~\\OneDrive - Adastra, s.r.o\\General - AWS Sales Team\\AWS Business\\AWS Partner Admin\\Launched ARR")
        self.downloaded_file_path = None
        self.destination_file_path = None
        self.previous_file_path = None
        self.new_records = []  # Completely new opportunities
        self.stage_changed_records = []  # Existing opportunities with stage changes

    def run(self):
        """Main method to run the export and processing workflow"""
        # Log script start with timestamp
        current_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logging.info(f"AWS Opportunity Export script started at {current_datetime}")
        logging.info("Starting AWS Opportunity Exporter")

        with sync_playwright() as p:
            # Launch browser and set up context with downloads enabled
            logging.info("Launching browser...")
            browser = p.chromium.launch(headless=self.headless)

            # Configure browser context with explicit download behavior
            logging.info("Configuring browser context...")
            context = browser.new_context(
                storage_state=self.storage_state,
                accept_downloads=True,
                viewport={'width': 1920, 'height': 1080}
            )

            # Create a new page
            page = context.new_page()

            # Add event listeners for download events
            context.on("download", lambda download: logging.info(f"Download started: {download.suggested_filename}"))

            logging.info("Browser and context set up with download handling enabled")

            try:
                # First verify login and navigate to Partner Central
                logging.info("Verifying login to AWS Partner Central...")
                if not self._verify_login(page):
                    logging.error("Login verification failed. Please run login_and_save_session_updated.py first.")
                    return

                # Export data (already on Partner Central from login verification)
                logging.info("Starting export process...")
                self._click_view_opportunities(page)
                self._export_opportunities(page)

                # Wait for download to complete and move file
                self._wait_for_download()
                self._move_file_to_destination()

                # Process the file
                self._find_previous_file()
                self._compare_with_previous_file()

                # Check if there are any meaningful changes before proceeding
                if self._has_meaningful_changes():
                    self._update_launched_date()
                    # Move previous file to Legacy folder as final step
                    self._move_previous_file_to_legacy()
                    logging.info("Process completed successfully!")
                else:
                    # No meaningful changes - log and clean up without creating new file
                    self._log_no_changes_and_cleanup()
                    logging.info("No meaningful changes found - process completed without creating new file")

            except Exception as e:
                logging.error(f"Error in main process: {e}", exc_info=True)

            finally:
                logging.info("Closing browser...")
                browser.close()

    def _verify_login(self, page):
        """Verify that the user is logged in to AWS Partner Central.

        Args:
            page: The Playwright page object

        Returns:
            bool: True if login is verified, False otherwise
        """
        try:
            logging.info("Navigating to AWS Partner Central for login verification...")
            page.goto("https://partnercentral.awspartner.com/partnercentral2/s/", timeout=120000)
            print(f"Current URL: {page.url}")

            # Wait for basic page load and give it time to render
            page.wait_for_load_state("domcontentloaded")
            time.sleep(3)

            # Check if we're redirected to login page - most reliable method
            current_url = page.url
            if "signin" in current_url.lower() or "login" in current_url.lower():
                logging.error("Redirected to login page. Session may have expired.")
                return False

            logging.info("Login verified - successfully loaded Partner Central page")
            return True

        except Exception as e:
            logging.error(f"Error verifying login: {e}")
            return False

    def _click_view_opportunities(self, page):
        """Click the View opportunities button.

        Args:
            page: The Playwright page object
        """
        print("Clicking View opportunities button...")
        try:
            # Check if button exists first
            button_count = page.locator("button:has-text('View opportunities')").count()
            print(f"Found {button_count} 'View opportunities' buttons")

            if button_count == 0:
                print("No 'View opportunities' button found. Checking page content...")
                # Check what's actually on the page
                page_title = page.title()
                current_url = page.url
                print(f"Page title: {page_title}")
                print(f"Current URL: {current_url}")

                # Look for any buttons with "opportunities" text
                opp_buttons = page.locator("button:has-text('opportunities')").count()
                print(f"Found {opp_buttons} buttons containing 'opportunities'")

                raise Exception("View opportunities button not found on page")

            # Click the View opportunities button
            page.locator("button:has-text('View opportunities')").first.click()
            print("Clicked View opportunities button")

            # Wait for page redirect to complete
            page.wait_for_load_state("domcontentloaded")
            time.sleep(3)

            # Verify we're on the correct opportunities page
            current_url = page.url
            if "pipeline-manager?tab=Opportunity" in current_url:
                print(f"Successfully navigated to opportunities view: {current_url}")
            else:
                print(f"Warning: Unexpected URL after clicking View opportunities: {current_url}")

        except Exception as e:
            print(f"Error clicking View opportunities button: {e}")
            raise

    def _export_opportunities(self, page):
        """Export opportunities using Bulk actions.

        Args:
            page: The Playwright page object
        """
        try:
            print("Exporting opportunities...")

            # Step 1: Click "Bulk actions" - wait a bit longer for page to be ready
            print("Looking for 'Bulk actions' button...")
            # Try to find the button by aria-label or title as well as text
            bulk_button = page.locator(
                "button:has-text('Bulk actions'), button[title='Bulk actions'], input[title='Bulk actions']"
            ).first
            bulk_button.wait_for(state="visible", timeout=15000)
            bulk_button.click()
            print("Clicked 'Bulk actions' button")

            # Give dropdown more time to open
            time.sleep(3)

            # Step 2: Select "Export Opportunities - All Opportunities"
            print("Looking for export option...")
            export_option = page.locator("span:has-text('Export Opportunities - All Opportunities')").first
            export_option.wait_for(state="visible", timeout=10000)

            with page.expect_download(timeout=60000) as download_info:
                export_option.click()
                print("Clicked 'Export Opportunities - All Opportunities'")

            # Step 3: Save the downloaded file
            download = download_info.value
            download_path = os.path.join(self.download_folder, download.suggested_filename)
            download.save_as(download_path)
            print(f"File downloaded to: {download_path}")
            self.downloaded_file_path = download_path

        except Exception as e:
            print(f"Error exporting opportunities: {e}")
            raise

    def _wait_for_download(self):
        """Wait for the download to complete."""
        if not self.downloaded_file_path:
            print("No download detected")
            return

        print(f"Waiting for download to complete: {self.downloaded_file_path}")

        # Wait for the file to exist
        max_wait_time = 60  # seconds
        start_time = time.time()

        while not os.path.exists(self.downloaded_file_path):
            if time.time() - start_time > max_wait_time:
                raise Exception(f"Timeout waiting for download: {self.downloaded_file_path}")
            time.sleep(1)

        # Wait for the file to be fully written
        file_size = -1
        while True:
            current_size = os.path.getsize(self.downloaded_file_path)
            if current_size == file_size:
                break
            file_size = current_size
            time.sleep(1)

        print(f"Download completed: {self.downloaded_file_path}")

    def _move_file_to_destination(self):
        """Move the downloaded file to the destination folder with timestamp and add Launched Date column."""
        if not self.downloaded_file_path:
            print("No file to move")
            return

        # Create destination folder if it doesn't exist
        os.makedirs(self.destination_folder, exist_ok=True)

        # Generate timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # Get file extension
        _, ext = os.path.splitext(self.downloaded_file_path)

        # Create destination filename with timestamp
        dest_filename = f"AWS_Opportunities_Export_{timestamp}{ext}"
        dest_path = os.path.join(self.destination_folder, dest_filename)

        print(f"Processing and moving file to: {dest_path}")

        try:
            # Read the downloaded Excel file
            df = pd.read_excel(self.downloaded_file_path)
            print(f"Loaded Excel file with {len(df)} rows and {len(df.columns)} columns")

            # Add 'Launched Date' column if it doesn't exist
            if 'Launched Date' not in df.columns:
                df['Launched Date'] = ''
                print("Added 'Launched Date' column to the Excel file")
            else:
                print("'Launched Date' column already exists in the file")

            # Remove unwanted columns
            columns_to_remove = [
                'Last Updated Date', 'Customer Email', 'Sales Activities', 'Customer Business Problem',
                'Solution Offered', 'Other Solution Offered', 'AWS Products', 'Additional Comments',
                'Next Step', 'Target Close Date', 'Closed Reason', 'Delivery Model',
                'Is Opportunity from Marketing Activity?', 'Marketing Campaign', 'Marketing Activity Channel',
                'Marketing Activity Use-Case', 'Was Marketing Development Funds Used?', 'APN Programs',
                'Other Competitors', 'Competitive Tracking', 'Use Case', 'Offer ID',
                'Partner Primary Need from AWS', 'AWS Account ID', 'Does opportunity belong to NatSec?',
                'Opportunity Type', 'Parent Opportunity ID', 'Customer DUNS', 'Partner CRM Unique Identifier',
                'Primary Contact First Name', 'Primary Contact Last Name', 'Primary Contact Email',
                'Primary Contact Phone', 'Primary Contact Title', 'Procurement Type', 'Customer Software Value',
                'Contract Effective Date/Term Start Date', 'Contract Expiration Date', 'Solution Tenancy',
                'Customer Software Value Currency', 'Status', 'Customer First Name', 'Customer Last Name',
                'Customer Title', 'Customer Phone', 'AWS Sales Rep Name', 'AWS Sales Rep Email',
                'Primary Contact Name', 'Lead Source', 'Created Date', 'Last Modified Date', 'Country',
                'Customer Website', 'Biz Unit', 'Date Submitted', 'Date Approved/Rejected', 'Industry Vertical',
                'Industry Other', 'Opportunity Ownership', 'Opportunity Owner Name', 'Opportunity Owner Email (Formula)',
                'AWS Partner Success Manager Name', 'AWS Partner Success Manager Email',
                'AWS ISV Success Manager (ISM) Name', 'AWS ISV Success Manager (ISM) Email',
                'Cosell Convert Reason', 'AWS Marketplace Engagement Score'
            ]

            # Remove columns that exist in the dataframe
            columns_removed = []
            for col in columns_to_remove:
                if col in df.columns:
                    df = df.drop(columns=[col])
                    columns_removed.append(col)

            if columns_removed:
                print(f"Removed {len(columns_removed)} unwanted columns from the Excel file")
            else:
                print("No unwanted columns found to remove")

            # Save the modified file to destination
            df.to_excel(dest_path, index=False)
            print(f"File saved with 'Launched Date' column and unwanted columns removed to: {dest_path}")

        except Exception as e:
            print(f"Error processing Excel file: {e}")
            print("Falling back to simple file copy...")
            # Fallback to simple copy if Excel processing fails
            shutil.copy2(self.downloaded_file_path, dest_path)

        # Set the destination file path
        self.destination_file_path = dest_path

        print(f"File processed and moved successfully to: {dest_path}")

    def _find_previous_file(self):
        """Find the previous file in the destination folder."""
        if not self.destination_file_path:
            print("No current file to compare with")
            return

        # Get all Excel files in the destination folder
        excel_files = glob.glob(os.path.join(self.destination_folder, "AWS_Opportunities_Export_*.xlsx"))

        if len(excel_files) <= 1:
            print("No previous files found for comparison")
            return

        # Sort files by modification time (newest first)
        excel_files.sort(key=os.path.getmtime, reverse=True)

        # Current file should be the newest, so previous is the second one
        for file in excel_files:
            if file != self.destination_file_path:
                self.previous_file_path = file
                print(f"Previous file found: {self.previous_file_path}")
                break

        if not self.previous_file_path:
            print("Could not identify previous file")

    def _compare_with_previous_file(self):
        """Compare current export with previous file to identify new records and stage changes."""
        if not self.destination_file_path or not self.previous_file_path:
            print("Missing files for comparison")
            return

        try:
            # Load both files
            current_df = pd.read_excel(self.destination_file_path)
            previous_df = pd.read_excel(self.previous_file_path)

            # Ensure required columns exist
            if 'Opportunity id' not in current_df.columns or 'Stage' not in current_df.columns:
                print("Error: Required columns not found in current file")
                return

            if 'Opportunity id' not in previous_df.columns or 'Stage' not in previous_df.columns:
                print("Error: Required columns not found in previous file")
                return

            # Add 'Estimated ARR' column before 'Launched Date' if it doesn't exist
            if 'Estimated ARR' not in current_df.columns:
                # Find the position to insert the column (before Launched Date if it exists)
                if 'Launched Date' in current_df.columns:
                    launched_date_pos = current_df.columns.get_loc('Launched Date')
                    # Insert Estimated ARR column before Launched Date
                    current_df.insert(launched_date_pos, 'Estimated ARR', 0)
                else:
                    # If Launched Date doesn't exist, add Estimated ARR at the end
                    current_df['Estimated ARR'] = 0
                print("Added 'Estimated ARR' column to the Excel file")

            # Calculate Estimated ARR from Monthly Recurring Revenue * 12
            if 'Estimated AWS Monthly Recurring Revenue' in current_df.columns:
                print("Calculating Estimated ARR values...")
                arr_calculated = 0

                for idx, row in current_df.iterrows():
                    monthly_revenue = row['Estimated AWS Monthly Recurring Revenue']

                    # Calculate ARR if monthly revenue has a valid numeric value
                    if pd.notna(monthly_revenue) and str(monthly_revenue).strip() != '':
                        try:
                            # Convert to float and multiply by 12
                            monthly_value = float(monthly_revenue)
                            arr_value = monthly_value * 12
                            current_df.loc[idx, 'Estimated ARR'] = arr_value
                            arr_calculated += 1
                        except (ValueError, TypeError):
                            # If conversion fails, set to 0
                            current_df.loc[idx, 'Estimated ARR'] = 0
                    else:
                        # If no monthly revenue, set ARR to 0
                        current_df.loc[idx, 'Estimated ARR'] = 0

                print(f"Calculated Estimated ARR for {arr_calculated} opportunities")
            else:
                print("Warning: 'Estimated AWS Monthly Recurring Revenue' column not found - setting all ARR values to 0")
                current_df['Estimated ARR'] = 0

            # Add 'Launched Date' column if it doesn't exist
            if 'Launched Date' not in current_df.columns:
                current_df['Launched Date'] = ''

            # Ensure 'Launched Date' column is string dtype to avoid pandas warnings
            current_df['Launched Date'] = current_df['Launched Date'].astype(str)

            # Copy existing 'Launched Date' values from previous file to current file
            if 'Launched Date' in previous_df.columns:
                print("Copying existing 'Launched Date' values from previous file...")
                print(f"Previous file has {len(previous_df)} rows")
                print(f"Current file has {len(current_df)} rows")

                # Check how many non-empty launched dates exist in previous file
                prev_non_empty = previous_df['Launched Date'].notna() & (previous_df['Launched Date'].astype(str).str.strip() != '') & (previous_df['Launched Date'].astype(str) != 'nan')
                print(f"Previous file has {prev_non_empty.sum()} non-empty 'Launched Date' values")

                # Print launched dates from previous file
                print("\n=== LAUNCHED DATE VALUES IN PREVIOUS FILE ===")
                prev_launched_count = 0
                for idx, row in previous_df.iterrows():
                    opp_id = row['Opportunity id']
                    launched_date = row['Launched Date']
                    stage = row['Stage']

                    # Only print if launched date has a value (exclude nan, NaN, empty strings)
                    if (pd.notna(launched_date) and
                        str(launched_date).strip() != '' and
                        str(launched_date).lower() != 'nan' and
                        str(launched_date) != 'NaN'):
                        prev_launched_count += 1
                        print(f"Previous Row {idx+1}: Opportunity {opp_id} | Stage: {stage} | Launched Date: '{launched_date}'")

                if prev_launched_count == 0:
                    print("No launched dates with values found in previous file")
                else:
                    print(f"Found {prev_launched_count} opportunities with launched dates in previous file")
                print("=== END OF PREVIOUS FILE LAUNCHED DATE VALUES ===\n")

                copied_dates = 0

                for _, prev_row in previous_df.iterrows():
                    prev_opp_id = prev_row['Opportunity id']
                    prev_launched_date = prev_row['Launched Date']

                    # Only copy if there's actually a launched date value (exclude nan, NaN, empty strings)
                    if (pd.notna(prev_launched_date) and
                        str(prev_launched_date).strip() != '' and
                        str(prev_launched_date).lower() != 'nan' and
                        str(prev_launched_date) != 'NaN'):
                        # Clean the launched date value - remove time portion if present
                        cleaned_date = str(prev_launched_date)
                        if ' 00:00:00' in cleaned_date:
                            cleaned_date = cleaned_date.replace(' 00:00:00', '')

                        # Find this opportunity in current file
                        current_mask = current_df['Opportunity id'] == prev_opp_id
                        if current_mask.any():
                            current_df.loc[current_mask, 'Launched Date'] = cleaned_date
                            copied_dates += 1
                            if cleaned_date != str(prev_launched_date):
                                print(f"Copied and cleaned launched date '{prev_launched_date}' -> '{cleaned_date}' for opportunity {prev_opp_id}")
                            else:
                                print(f"Copied launched date '{cleaned_date}' for opportunity {prev_opp_id}")
                        else:
                            print(f"Opportunity {prev_opp_id} with launched date '{prev_launched_date}' not found in current file")

                print(f"Copied {copied_dates} existing 'Launched Date' values to current file")
            else:
                print("No 'Launched Date' column found in previous file")

            # Print only Launched Date values that have content
            print("\n=== LAUNCHED DATE VALUES WITH CONTENT ===")
            launched_count = 0
            for idx, row in current_df.iterrows():
                opp_id = row['Opportunity id']
                launched_date = row['Launched Date']
                stage = row['Stage']

                # Only print if launched date has a value (exclude nan, NaN, empty strings)
                if (pd.notna(launched_date) and
                    str(launched_date).strip() != '' and
                    str(launched_date).lower() != 'nan' and
                    str(launched_date) != 'NaN'):
                    launched_count += 1
                    print(f"Row {idx+1}: Opportunity {opp_id} | Stage: {stage} | Launched Date: '{launched_date}'")

            if launched_count == 0:
                print("No launched dates with values found in current file")
            else:
                print(f"Found {launched_count} opportunities with launched dates")
            print("=== END OF LAUNCHED DATE VALUES ===\n")

            # Find completely new records
            current_ids = set(current_df['Opportunity id'])
            previous_ids = set(previous_df['Opportunity id'])
            new_ids = current_ids - previous_ids
            self.new_records = list(new_ids)

            # Find existing records with stage changes
            for _, row in current_df.iterrows():
                opp_id = row['Opportunity id']
                current_stage = row['Stage']

                # Skip if this is a new record (already handled)
                if opp_id in new_ids:
                    continue

                # Find this opportunity in previous file
                prev_record = previous_df[previous_df['Opportunity id'] == opp_id]
                if not prev_record.empty:
                    prev_stage = prev_record['Stage'].values[0]

                    # Check if stage has changed
                    if current_stage != prev_stage:
                        self.stage_changed_records.append((opp_id, prev_stage, current_stage))

            print(f"Found {len(self.new_records)} new records")
            print(f"Found {len(self.stage_changed_records)} records with stage changes")

            # Save the dataframe for later updating
            self.current_df = current_df

            # Clean up any remaining 'nan' values in Launched Date column before saving
            print("Final cleanup: removing any 'nan' values from Launched Date column...")
            mask_to_clean = (
                (self.current_df['Launched Date'].astype(str).str.lower() == 'nan') |
                (self.current_df['Launched Date'].astype(str) == 'NaN') |
                (self.current_df['Launched Date'].isna())
            )
            cleaned_count = mask_to_clean.sum()
            if cleaned_count > 0:
                self.current_df.loc[mask_to_clean, 'Launched Date'] = ''
                print(f"Cleaned {cleaned_count} 'nan' values from Launched Date column")
            else:
                print("No 'nan' values found to clean")

            # Save the file with proper date formatting for Launched Date column
            self._save_excel_with_date_format()

            if 'Launched Date' in previous_df.columns and copied_dates > 0:
                print(f"Saved file with {copied_dates} copied launched dates to preserve them")
            else:
                print("Saved file with cleaned Launched Date column")

        except Exception as e:
            print(f"Error comparing files: {e}")

    def _save_excel_with_date_format(self):
        """Save Excel file with proper Date format for Launched Date column."""
        try:
            # Convert Launched Date values to proper datetime objects
            launched_date_col = self.current_df['Launched Date'].copy()

            for idx, date_val in enumerate(launched_date_col):
                if pd.notna(date_val) and str(date_val).strip() != '' and str(date_val).lower() != 'nan':
                    try:
                        # Clean the date string first - remove time portion if present
                        date_str = str(date_val).strip()
                        if ' 00:00:00' in date_str:
                            date_str = date_str.replace(' 00:00:00', '')

                        # Try to parse as date and convert to datetime object
                        parsed_date = pd.to_datetime(date_str, errors='coerce')
                        if pd.notna(parsed_date):
                            # Store as datetime object for proper Excel date formatting
                            self.current_df.loc[idx, 'Launched Date'] = parsed_date
                    except:
                        # If parsing fails, keep as string
                        pass

            # Save with openpyxl engine and apply date formatting
            with pd.ExcelWriter(self.destination_file_path, engine='openpyxl') as writer:
                self.current_df.to_excel(writer, sheet_name='Sheet1', index=False)

                # Get the worksheet to apply formatting
                worksheet = writer.sheets['Sheet1']

                # Find the Launched Date column
                if 'Launched Date' in self.current_df.columns:
                    launched_date_col_idx = self.current_df.columns.get_loc('Launched Date') + 1  # +1 for Excel 1-based indexing

                    # Apply date format to the entire Launched Date column
                    from openpyxl.styles import NamedStyle

                    # Create a date style
                    date_style = NamedStyle(name='date_style', number_format='YYYY-MM-DD')

                    # Apply to all cells in the Launched Date column (starting from row 2 to skip header)
                    for row in range(2, len(self.current_df) + 2):
                        cell = worksheet.cell(row=row, column=launched_date_col_idx)
                        if cell.value is not None and cell.value != '':
                            cell.style = date_style

                    print("Applied Date format (YYYY-MM-DD) to 'Launched Date' column")

            print("Saved Excel file with proper Date formatting")

        except Exception as e:
            print(f"Error saving Excel with date format: {e}")
            # Fallback to regular save
            self.current_df.to_excel(self.destination_file_path, index=False)
            print("Saved with fallback method (no date formatting)")

    def _update_launched_date(self):
        """Update the Launched Date column for new records with 'launched' stage and stage changes."""
        if not hasattr(self, 'current_df'):
            print("No dataframe to update")
            return

        today = datetime.datetime.now().strftime("%Y-%m-%d")
        updated_new_records = 0

        # Update new records only if they have 'launched' stage
        if self.new_records:
            print(f"Checking {len(self.new_records)} new records for 'launched' stage...")
            for opp_id in self.new_records:
                mask = self.current_df['Opportunity id'] == opp_id
                record = self.current_df[mask]

                if not record.empty:
                    current_stage = record['Stage'].values[0]
                    # Check if the stage contains 'launched' (case-insensitive)
                    if 'launched' in str(current_stage).lower():
                        self.current_df.loc[mask, 'Launched Date'] = today
                        updated_new_records += 1
                        print(f"New opportunity {opp_id} has 'launched' stage: {current_stage} - setting launched date")
                    else:
                        print(f"New opportunity {opp_id} has stage: {current_stage} - not setting launched date")

        # Update records with stage changes to 'launched'
        launched_stage_changes = 0
        if self.stage_changed_records:
            print(f"Checking {len(self.stage_changed_records)} records with stage changes...")
            for opp_id, old_stage, new_stage in self.stage_changed_records:
                # Only update if the new stage contains 'launched' (case-insensitive)
                if 'launched' in str(new_stage).lower():
                    mask = self.current_df['Opportunity id'] == opp_id
                    self.current_df.loc[mask, 'Launched Date'] = today
                    launched_stage_changes += 1
                    print(f"Opportunity {opp_id} changed from '{old_stage}' to '{new_stage}' - setting launched date")

        # Save the updated dataframe back to the file with proper date formatting
        if updated_new_records > 0 or launched_stage_changes > 0:
            self._save_excel_with_date_format()
            print(f"Updated file saved to {self.destination_file_path}")

        print(f"\nSummary:")
        print(f"Total new records: {len(self.new_records)}")
        print(f"New records with 'launched' stage (updated): {updated_new_records}")
        print(f"Stage changes to 'launched' (updated): {launched_stage_changes}")
        print(f"Total records updated with launched date: {updated_new_records + launched_stage_changes}")

    def _move_previous_file_to_legacy(self):
        """Move the previous file to the Legacy folder as the final step."""
        if not hasattr(self, 'previous_file_path') or not self.previous_file_path:
            print("No previous file to move to Legacy folder")
            return

        try:
            legacy_folder = r"C:\Users\<USER>\OneDrive - Adastra, s.r.o\General - AWS Sales Team\AWS Business\AWS Partner Admin\Launched ARR\Legacy"

            # Create Legacy folder if it doesn't exist
            os.makedirs(legacy_folder, exist_ok=True)
            print(f"Legacy folder ensured: {legacy_folder}")

            # Get the filename from the previous file path
            previous_filename = os.path.basename(self.previous_file_path)
            legacy_file_path = os.path.join(legacy_folder, previous_filename)

            # Check if the previous file still exists
            if os.path.exists(self.previous_file_path):
                # Move the file to Legacy folder
                shutil.move(self.previous_file_path, legacy_file_path)
                print(f"Moved previous file to Legacy folder:")
                print(f"  From: {self.previous_file_path}")
                print(f"  To: {legacy_file_path}")
            else:
                print(f"Previous file not found (may have been moved already): {self.previous_file_path}")

        except Exception as e:
            print(f"Error moving previous file to Legacy folder: {e}")
            print("Continuing without moving the file...")

    def _has_meaningful_changes(self):
        """Check if there are meaningful changes that warrant creating a new file."""
        if not hasattr(self, 'new_records') or not hasattr(self, 'stage_changed_records'):
            print("No comparison data available")
            return False

        # Check for new opportunities
        has_new_opportunities = len(self.new_records) > 0

        # Check for stage changes to 'Launched'
        has_launched_stage_changes = False
        if len(self.stage_changed_records) > 0:
            for stage_change in self.stage_changed_records:
                # stage_change is a tuple: (opp_id, prev_stage, current_stage)
                opp_id, prev_stage, current_stage = stage_change
                if current_stage.lower() == 'launched':
                    has_launched_stage_changes = True
                    print(f"Found stage change to 'Launched': Opportunity {opp_id} changed from '{prev_stage}' to '{current_stage}'")
                    break

        # Log the meaningful changes check results
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")

        print(f"Meaningful changes check:")
        print(f"  New opportunities: {len(self.new_records)} ({'Yes' if has_new_opportunities else 'No'})")
        print(f"  Stage changes to 'Launched': {'Yes' if has_launched_stage_changes else 'No'}")

        # Log to file
        if has_new_opportunities or has_launched_stage_changes:
            message = f"On {current_date}: Found meaningful changes - New opportunities: {len(self.new_records)}, Stage changes to 'Launched': {'Yes' if has_launched_stage_changes else 'No'}"
            logging.info(message)
        else:
            message = f"On {current_date}: No meaningful changes found - No new opportunities, no stage changes to 'Launched'"
            logging.info(message)

        return has_new_opportunities or has_launched_stage_changes

    def _log_no_changes_and_cleanup(self):
        """Log that no meaningful changes were found and clean up the downloaded file."""
        from datetime import datetime

        current_date = datetime.now().strftime("%Y-%m-%d")

        # Log to both console and log file
        message = f"On {current_date}: No records have a stage change to 'Launched', no new opportunities created on {current_date}"
        print(f"\n{message}")
        logging.info(message)

        # Clean up the downloaded file from Downloads folder since we're not keeping it
        try:
            # Delete the original downloaded file from Downloads folder
            if hasattr(self, 'downloaded_file_path') and self.downloaded_file_path and os.path.exists(self.downloaded_file_path):
                os.remove(self.downloaded_file_path)
                print(f"Cleaned up original downloaded file from Downloads: {self.downloaded_file_path}")
                logging.info(f"Cleaned up original downloaded file from Downloads: {self.downloaded_file_path}")

            # Also delete the processed file from destination folder since we're not keeping it
            if hasattr(self, 'destination_file_path') and self.destination_file_path and os.path.exists(self.destination_file_path):
                os.remove(self.destination_file_path)
                print(f"Cleaned up processed file from destination: {self.destination_file_path}")
                logging.info(f"Cleaned up processed file from destination: {self.destination_file_path}")
        except Exception as e:
            print(f"Error cleaning up downloaded files: {e}")
            logging.error(f"Error cleaning up downloaded files: {e}")


# Run the export and comparison
if __name__ == "__main__":
    logging.info("=" * 80)
    logging.info("Starting AWS Opportunity Export and Comparison")
    logging.info("=" * 80)

    # Check for aws_session.json in multiple locations
    session_locations = [
        "aws_session.json",  # Current directory
        os.path.join("..", "aws_session.json"),  # Parent directory
        os.path.join("..", "AWSFundRequest", "aws_session.json"),  # AWSFundRequest folder
    ]

    session_file = None
    for location in session_locations:
        if os.path.exists(location):
            session_file = location
            logging.info(f"Found session file: {session_file}")
            break

    if session_file:
        exporter = AWSOpportunityExporter(headless=False, storage_state=session_file)
    else:
        logging.error("aws_session.json not found in any expected location. Please run login_and_save_session_updated.py first.")
        logging.error(f"Checked locations: {session_locations}")
        exit(1)

    try:
        exporter.run()
        logging.info("Script execution completed")
    except Exception as e:
        logging.error(f"Unhandled exception in main: {e}", exc_info=True)
        exit(1)









