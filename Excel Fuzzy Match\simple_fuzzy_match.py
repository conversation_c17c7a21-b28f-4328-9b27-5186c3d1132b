import pandas as pd
from rapidfuzz import process, fuzz
import os

# Define file paths
target_file_path = r"C:\Users\<USER>\Downloads\Fuzzy Match Target - ENT GFC Propensity List OCT.xlsx"
account_list_file_path = r"C:\Users\<USER>\Downloads\JT - CA & US Accounts 2025_05_02 2-44-05 PM.xlsx"
output_file_path = r"C:\Users\<USER>\Downloads\Fuzzy Match Target - ENT GFC Propensity List OCT - Updated.xlsx"

print(f"Loading target file: {os.path.basename(target_file_path)}")
print(f"Loading account list file: {os.path.basename(account_list_file_path)}")

# Load the Excel files
target_df = pd.read_excel(target_file_path, sheet_name="Sheet1")
account_df = pd.read_excel(account_list_file_path, sheet_name="JT - CA & US Accounts")

print(f"Target file loaded with {len(target_df)} rows")
print(f"Account list file loaded with {len(account_df)} rows")

# Define column names
target_company_col = "gtm_customer_name"
target_ae_col = "Adastra AE Name"
account_company_col = "Account Name"
account_ae_col = "Owner"

# Verify columns exist
if target_company_col not in target_df.columns:
    print(f"Column '{target_company_col}' not found in target file")
    print("Available columns:", target_df.columns.tolist())
    exit(1)

if target_ae_col not in target_df.columns:
    print(f"Column '{target_ae_col}' not found in target file. Creating it.")
    target_df[target_ae_col] = ""

if account_company_col not in account_df.columns:
    print(f"Column '{account_company_col}' not found in account list file")
    print("Available columns:", account_df.columns.tolist())
    exit(1)

if account_ae_col not in account_df.columns:
    print(f"Column '{account_ae_col}' not found in account list file")
    print("Available columns:", account_df.columns.tolist())
    exit(1)

# Clean company names for matching
print("Preparing data for fuzzy matching...")
target_df['clean_name'] = target_df[target_company_col].fillna('').astype(str).str.lower().str.strip()
account_df['clean_name'] = account_df[account_company_col].fillna('').astype(str).str.lower().str.strip()

# Create a dictionary of account names to owner names for faster lookup
account_dict = dict(zip(account_df['clean_name'], account_df[account_ae_col]))

# Create a list of all account names for matching
all_account_names = account_df['clean_name'].tolist()

# Set match threshold
threshold = 80
print(f"Using match threshold: {threshold}")

# Perform fuzzy matching
print("Performing fuzzy matching...")
matches = []
match_count = 0
no_match_count = 0

# Process each row in the target file
for idx, row in target_df.iterrows():
    company_name = row['clean_name']

    # Skip empty values
    if not company_name or company_name == "nan":
        continue

    # Perform fuzzy matching
    match_result = process.extractOne(company_name, all_account_names, scorer=fuzz.token_sort_ratio)

    if match_result and match_result[1] >= threshold:
        match_name, score, match_idx = match_result
        ae_name = account_dict.get(match_name, "")

        # Update the AE name in the target dataframe
        target_df.at[idx, target_ae_col] = ae_name

        matches.append({
            'Target Company': row[target_company_col],
            'Matched Account': account_df.iloc[match_idx][account_company_col],
            'Match Score': score,
            'AE Name': ae_name
        })
        match_count += 1

        # Print progress every 50 matches
        if match_count % 50 == 0:
            print(f"Processed {match_count + no_match_count} companies. Found {match_count} matches so far.")
    else:
        no_match_count += 1

# Remove the temporary column
target_df = target_df.drop('clean_name', axis=1)

# Save the updated target file
print(f"Saving updated target file to: {os.path.basename(output_file_path)}")
target_df.to_excel(output_file_path, sheet_name="Sheet1", index=False)

# Save the matches to a separate file
matches_df = pd.DataFrame(matches)
matches_file = "Fuzzy_Matching_Results.xlsx"
matches_df.to_excel(matches_file, index=False)

print(f"Found {match_count} matches above threshold.")
print(f"No match found for {no_match_count} companies.")
print(f"Updated target file saved to: {os.path.basename(output_file_path)}")
print(f"Matching results saved to: {matches_file}")
