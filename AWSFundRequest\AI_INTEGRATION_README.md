# AWS Partner Funding Portal - AI Integration

This document explains how to use the AI integration features added to the FundRequestExportComparer tool.

## Overview

The AI integration enhances the FundRequestExportComparer tool with the following capabilities:

1. **Customer Name Extraction**: Uses AI to extract customer names from business descriptions for non-MDF fund requests.
2. **Fund Request Categorization**: Automatically categorizes fund requests based on their descriptions and programs.
3. **Summary Analysis**: Provides enhanced summary information including totals by category.

## Prerequisites

1. Python 3.8 or higher
2. Required Python packages:
   - openai
   - pandas
   - playwright
   - openpyxl
3. An OpenAI API key (sign up at https://platform.openai.com/)

## Installation

1. Install the required packages:
   ```
   pip install openai pandas playwright openpyxl
   ```

2. Install Playwright browsers:
   ```
   python -m playwright install
   ```

3. Set up your OpenAI API key as an environment variable:
   ```
   # On Windows
   set OPENAI_API_KEY=your_api_key_here
   
   # On macOS/Linux
   export OPENAI_API_KEY=your_api_key_here
   ```

## Usage

### Running with AI Integration

1. Run the script with AI integration:
   ```
   python run_with_ai.py
   ```

2. If you haven't set the OpenAI API key as an environment variable, you'll be prompted to enter it.

3. Choose whether to run in headless mode.

4. The script will:
   - Export fund requests from the AWS Partner Funding Portal
   - Compare with the previous month's data
   - Use AI to extract customer names and categorize fund requests
   - Generate a summary with totals by category

### Output

The script generates an Excel file with the following sheets:

1. **Fund Requests**: The main data with extracted information
2. **Summary**: A summary of the data including totals by category
3. **Currency Exchange Rates**: Exchange rates used for currency conversion

## AI Features

### Customer Name Extraction

For non-MDF fund requests, the tool uses AI to extract customer names from business descriptions. This works by:

1. First looking for patterns like "Customer: XYZ" or "Client: XYZ"
2. If no pattern is found, using AI to analyze the text and identify the customer

### Fund Request Categorization

The tool categorizes fund requests into the following categories:

- Marketing
- Technical
- Training
- Business Development
- Other

This categorization is based on the program type and business description.

### Customization

You can customize the AI behavior by modifying the `ai_helper.py` file:

- Change the model used (e.g., from "gpt-3.5-turbo" to "gpt-4")
- Adjust the temperature parameter for more deterministic or creative results
- Add new AI-powered features

## Troubleshooting

### AI Features Not Working

1. Verify your OpenAI API key is correct
2. Check your internet connection
3. Ensure you have sufficient credits in your OpenAI account

### Rate Limiting

If you encounter rate limiting issues with the OpenAI API, you can:

1. Reduce the number of requests by processing fewer records
2. Add delays between API calls
3. Upgrade your OpenAI account for higher rate limits

## Support

For questions or issues with the AI integration, please contact the developer.
