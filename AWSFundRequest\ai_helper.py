import os
import openai
from typing import Optional, List, Dict, Any

class AIHelper:
    """Helper class to interact with OpenAI's API for generative AI capabilities."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the AIHelper with an API key.
        
        Args:
            api_key: OpenAI API key. If None, will try to get from environment variable OPENAI_API_KEY.
        """
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key is required. Please provide it or set OPENAI_API_KEY environment variable.")
        
        self.client = openai.OpenAI(api_key=self.api_key)
    
    def extract_customer_from_description(self, description: str) -> str:
        """Extract customer name from a business description using AI.
        
        Args:
            description: The business description text.
            
        Returns:
            Extracted customer name or empty string if not found.
        """
        if not description:
            return ""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",  # You can use other models like "gpt-4" for better results
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that extracts customer names from business descriptions."},
                    {"role": "user", "content": f"Extract the customer name from this business description. Only return the customer name, nothing else. If you can't find a clear customer name, return 'Unknown'.\n\nDescription: {description}"}
                ],
                temperature=0.3,  # Lower temperature for more deterministic results
                max_tokens=50
            )
            
            customer_name = response.choices[0].message.content.strip()
            
            # Clean up the response
            if customer_name.lower() == "unknown":
                return ""
                
            return customer_name
        except Exception as e:
            print(f"Error extracting customer name with AI: {e}")
            return ""
    
    def categorize_fund_request(self, description: str, program: str) -> Dict[str, Any]:
        """Categorize a fund request based on its description and program.
        
        Args:
            description: The business description text.
            program: The program name.
            
        Returns:
            Dictionary with categorization information.
        """
        if not description or not program:
            return {"category": "Unknown", "confidence": 0.0}
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that categorizes AWS Partner funding requests."},
                    {"role": "user", "content": f"Categorize this AWS Partner funding request based on the description and program. Return a JSON object with 'category' (one of: Marketing, Technical, Training, Business Development, Other) and 'confidence' (0.0 to 1.0).\n\nProgram: {program}\nDescription: {description}"}
                ],
                temperature=0.3,
                max_tokens=100,
                response_format={"type": "json_object"}
            )
            
            import json
            result = json.loads(response.choices[0].message.content)
            return result
        except Exception as e:
            print(f"Error categorizing fund request with AI: {e}")
            return {"category": "Unknown", "confidence": 0.0}
    
    def summarize_business_description(self, description: str, max_words: int = 50) -> str:
        """Summarize a business description using AI.
        
        Args:
            description: The business description text.
            max_words: Maximum number of words in the summary.
            
        Returns:
            Summarized description.
        """
        if not description:
            return ""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that summarizes business descriptions concisely."},
                    {"role": "user", "content": f"Summarize this business description in {max_words} words or less:\n\n{description}"}
                ],
                temperature=0.5,
                max_tokens=100
            )
            
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"Error summarizing business description with AI: {e}")
            return ""

# Example usage
if __name__ == "__main__":
    # For testing purposes
    api_key = input("Enter your OpenAI API key: ")
    ai_helper = AIHelper(api_key)
    
    test_description = "This project is for ABC Corporation to implement a new cloud migration strategy using AWS services."
    
    customer = ai_helper.extract_customer_from_description(test_description)
    print(f"Extracted customer: {customer}")
    
    category = ai_helper.categorize_fund_request(test_description, "Migration Acceleration Program")
    print(f"Categorization: {category}")
    
    summary = ai_helper.summarize_business_description(test_description)
    print(f"Summary: {summary}")
