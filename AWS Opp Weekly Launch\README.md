# AWS Opportunity Weekly Launch Automation

This script automates the process of exporting AWS opportunities from the ACE pipeline manager and tracking newly launched opportunities.

## Features

- Automatically logs into AWS Partner Central using saved session
- Navigates to the ACE pipeline manager
- Clicks "View opportunities" button
- Uses "Bulk actions" to export all opportunities
- Moves the exported file to the destination folder with a timestamp
- Compares with the previous export to identify new records
- Adds the current date to the "Launched Date" column for new records

## Prerequisites

1. Python 3.8 or higher
2. Required Python packages:
   - pandas
   - playwright
   - openpyxl

## Installation

1. Install the required packages:
   ```
   pip install pandas playwright openpyxl
   ```

2. Install Playwright browsers:
   ```
   python -m playwright install
   ```

## Usage

### Important: Always Run Login Script First

**The script assumes you have already run the login script to create a valid session.**

### Step 1: Login and Save Session

First, you need to log in and save your AWS Partner Central session:

```
python login_and_save_session_updated.py
```

This will:
- Open a browser window
- Navigate to AWS Partner Central
- Prompt you to log in manually
- Save your session to `aws_session.json`

### Step 2: Run the Opportunity Exporter

After saving your session, run the opportunity exporter:

```
python aws_opp_exporter.py
```

**Note:** The script assumes the session is valid and will not verify login status. If you encounter issues, make sure to run the login script again to refresh your session.

This will:
- Use your saved session to access AWS Partner Central
- Navigate to the ACE pipeline manager
- Click "View opportunities"
- Export all opportunities
- Move the file to the destination folder
- Compare with previous exports
- Mark new records with today's date in the "Launched Date" column

## Output

The script will:
1. Save the exported file to: `C:\Users\<USER>\OneDrive - Adastra, s.r.o\General - AWS Sales Team\AWS Business\AWS Partner Admin\Launched ARR`
2. Name the file with a timestamp: `AWS_Opportunities_Export_YYYYMMDD_HHMMSS.xlsx`
3. Print a summary of new records found

## Troubleshooting

If you encounter issues:

1. **Session expired**: Run `login_and_save_session_updated.py` again to refresh your session
2. **Download issues**: Check your Downloads folder for the most recent Excel file
3. **Navigation problems**: Look at the screenshots saved during execution for clues

## Screenshots

The script saves several screenshots during execution to help with troubleshooting:
- `login_verification.png`: Verification of login status
- `ace_pipeline.png`: ACE pipeline manager page
- `opportunities_view.png`: Opportunities view
- `error_screenshot.png`: Screenshot taken if an error occurs
