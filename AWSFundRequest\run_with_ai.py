import os
import sys
from AWSFundRequest.FundRequestExportComparer import FundRequestExportComparer

def main():
    """Run the FundRequestExportComparer with AI integration."""
    print("AWS Partner Funding Portal - Export Comparer with AI Integration")
    print("=" * 70)
    
    # Check if OpenAI API key is provided
    openai_api_key = os.environ.get("OPENAI_API_KEY")
    if not openai_api_key:
        print("\nOpenAI API key not found in environment variables.")
        print("You can provide it now or leave blank to run without AI features.")
        openai_api_key = input("Enter your OpenAI API key (or press Enter to skip): ").strip()
        if openai_api_key:
            os.environ["OPENAI_API_KEY"] = openai_api_key
        else:
            print("Running without AI features.")
            openai_api_key = None
    
    # Ask if the user wants to run in headless mode
    headless_input = input("\nRun in headless mode? (y/n, default: n): ").strip().lower()
    headless = headless_input == 'y'
    
    # Create and run the exporter
    try:
        exporter = FundRequestExportComparer(
            headless=headless,
            storage_state="aws_session.json",
            openai_api_key=openai_api_key
        )
        exporter.run()
        print("\nProcess completed successfully!")
    except Exception as e:
        print(f"\nError running the exporter: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
