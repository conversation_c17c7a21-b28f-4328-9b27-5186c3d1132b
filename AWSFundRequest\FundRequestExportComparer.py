from playwright.sync_api import sync_playwright
import pandas as pd
import os
import time
import datetime
import shutil
from pathlib import Path
import re
import glob

# Import the AI Helper
try:
    from AWSFundRequest.ai_helper import AIHelper
except ImportError:
    print("AI Helper module not found. Some AI features will be disabled.")
    AIHelper = None

class FundRequestExportComparer:
    def __init__(self, headless=False, storage_state="aws_session.json", openai_api_key=None):
        self.headless = headless
        self.storage_state = storage_state
        # Use standard Windows Downloads folder path
        self.download_folder = os.path.join(os.path.expanduser("~"), "Downloads")
        self.destination_folder = os.path.expanduser("~\\OneDrive - Adastra, s.r.o\\General - AWS Sales Team\\AWS Business\\AWS Partner Admin\\POC & MAP Funding")

        # Initialize AI Helper if available
        self.ai_helper = None
        if AIHelper is not None:
            try:
                self.ai_helper = AIHelper(api_key=openai_api_key)
                print("AI Helper initialized successfully")
            except Exception as e:
                print(f"Failed to initialize AI Helper: {e}")
                print("AI features will be disabled")

        print(f"Download folder set to: {self.download_folder}")
        print(f"Destination folder set to: {self.destination_folder}")

        # Get current date information
        self.current_date = datetime.datetime.now()
        self.current_month = self.current_date.month
        self.previous_month = 12 if self.current_month == 1 else self.current_month - 1
        self.previous_month_str = f"{self.previous_month:02d}"

        self.today_date = self.current_date.strftime("%m-%d-%Y")
        self.expected_filename_pattern = f"{self.today_date}-FundingActivities.csv"
        print(f"Expected filename pattern: {self.expected_filename_pattern}")

        self.downloaded_file_path = None
        self.destination_file_path = None
        self.previous_month_file = None
        self.new_records = []

    def run(self):
        """Main method to run the export and comparison process"""
        with sync_playwright() as p:
            # Launch browser and set up context with downloads enabled
            browser = p.chromium.launch(headless=self.headless)

            # Configure browser context with explicit download behavior
            context = browser.new_context(
                storage_state=self.storage_state,
                accept_downloads=True,
                # Ensure downloads are handled properly
                viewport={'width': 1920, 'height': 1080}
            )

            # Create a new page
            page = context.new_page()

            # Add event listeners for download events
            context.on("download", lambda download: print(f"Download started: {download.suggested_filename}"))

            print("Browser and context set up with download handling enabled")

            try:
                # Verify login status before proceeding
                print("Verifying login status...")
                if not self._verify_login(page):
                    print("Login verification failed. Please run login_and_save_session.py first.")
                    return

                # Navigate to dashboard and export data
                print("Starting export process...")
                self._navigate_to_dashboard(page)
                self._click_export_button(page)

                # Wait for download to complete and move file
                self._wait_for_download()
                self._move_file_to_destination()

                # Process the file
                self._find_previous_month_file()
                self._compare_with_previous_month()

                # Extract additional data using the same browser instance
                if self.new_records:
                    self._extract_additional_data(page)
                else:
                    print("No new records to extract additional data for")

                self._save_processed_file()

                print(f"Export and comparison complete!")
                print(f"Found {len(self.new_records)} new records this month.")
                print(f"Enhanced data saved to {self.destination_file_path}")

            except Exception as e:
                print(f"Error in main process: {e}")
                # Log the error and continue

            finally:
                print("Closing browser...")
                browser.close()

    def _verify_login(self, page):
        """Verify that we have a valid login session"""
        try:
            # Navigate to the dashboard
            print("Navigating to dashboard to verify login...")
            page.goto("https://funding.awspartner.com/dashboard")
            page.wait_for_load_state("networkidle", timeout=30000)

            # Check if we're redirected to an auth page
            current_url = page.url
            print(f"Current URL after navigation: {current_url}")

            if "/auth" in current_url or "signin" in current_url.lower():
                print("Redirected to authentication page. Session may be invalid or expired.")
                return False

            # Look for elements that would indicate we're logged in
            dashboard_indicators = [
                "Funding Activities",
                "My Wallet",
                "Dashboard"
            ]

            for indicator in dashboard_indicators:
                indicator_locator = page.locator(f"text={indicator}").first
                if indicator_locator.count() > 0:
                    print(f"Found dashboard indicator: {indicator}")
                    return True

            print("Could not find any dashboard indicators. Login may have failed.")
            return False

        except Exception as e:
            print(f"Error verifying login: {e}")
            return False

    def _navigate_to_dashboard(self, page):
        """Navigate to the funding dashboard and wait for it to load"""
        print("Navigating to Funding Dashboard...")
        page.goto("https://funding.awspartner.com/dashboard")

        # Wait for navigation and authentication to complete
        page.wait_for_load_state("networkidle", timeout=30000)

        # Check if we're on the correct page
        current_url = page.url

        if "/auth" in current_url:
            print("Still on authentication page. Waiting for redirect...")
            # Wait for redirect to dashboard
            page.wait_for_url("**/dashboard", timeout=30000)

        # Additional wait to ensure page is fully rendered
        time.sleep(5)

        # Verify we're on the dashboard by looking for Funding Activities
        funding_activities = page.locator("text=Funding Activities").first
        if funding_activities.count() == 0:
            raise Exception("Could not find Funding Activities on dashboard")

        print("Successfully navigated to dashboard")

    def _click_export_button(self, page):
        """Click the Export button in the Funding Activities section"""
        print("Looking for Export button in Funding Activities section...")

        # First, locate the Funding Activities section
        print("Locating Funding Activities section...")
        funding_activities_section = None

        # Try different approaches to find the Funding Activities section
        funding_section_locators = [
            # Look for the heading with text
            page.locator("h2:has-text('Funding Activities'), h3:has-text('Funding Activities')").first,
            # Look for the div containing the text
            page.locator("div:has-text('Funding Activities')").first,
            # Look for any element with the text that might be a container
            page.locator(":text('Funding Activities')").first
        ]

        for locator in funding_section_locators:
            if locator.count() > 0:
                # Found the heading, now get the containing section
                funding_activities_section = locator
                print("Found Funding Activities section")
                break

        if not funding_activities_section:
            raise Exception("Could not find Funding Activities section")

        # Now find the Export button within or near the Funding Activities section
        print("Looking for Export button within Funding Activities section...")

        # Try to find the Export button near the Funding Activities section
        export_button = None

        # Method 1: Look for the Export button that's a sibling or child of the section
        try:
            # Try to find the container that has both Funding Activities and the Export button
            container = page.locator("div:has-text('Funding Activities'):has(button:has-text('Export'))").first
            if container.count() > 0:
                # Find the Export button within this container
                export_button = container.locator("button:has-text('Export')").first
                if export_button.count() > 0:
                    print("Found Export button within Funding Activities container")
        except Exception as e:
            print(f"Method 1 failed: {e}")

        # Method 2: Look for the Export button that appears after the Funding Activities heading
        if not export_button or export_button.count() == 0:
            try:
                # Get all Export buttons
                all_export_buttons = page.locator("button:has-text('Export')").all()

                # Find the one that's closest to the Funding Activities section
                # This is a simplified approach - in a real scenario, we'd need to check positions
                if all_export_buttons:
                    # For now, just take the first one after finding Funding Activities
                    export_button = all_export_buttons[0]
                    print("Found first Export button after locating Funding Activities")
            except Exception as e:
                print(f"Method 2 failed: {e}")

        # Method 3: Look for the Export button based on its position relative to other elements
        if not export_button or export_button.count() == 0:
            try:
                # Look for the Export button that's near the Create Fund Request button
                create_button = page.locator("button:has-text('Create Fund Request')").first
                if create_button.count() > 0:
                    # The Export button should be nearby
                    nearby_export = page.locator("button:has-text('Export'):near(button:has-text('Create Fund Request'))").first
                    if nearby_export.count() > 0:
                        export_button = nearby_export
                        print("Found Export button near Create Fund Request button")
            except Exception as e:
                print(f"Method 3 failed: {e}")

        # Method 4: Use the screenshot to identify the Export button visually
        if not export_button or export_button.count() == 0:
            try:
                # Take a screenshot to help with debugging
                page.screenshot(path="dashboard_screenshot.png")
                print("Saved screenshot to dashboard_screenshot.png for debugging")

                # Try one more approach - look for buttons in the top section of the page
                top_buttons = page.locator("button").all()
                for button in top_buttons:
                    try:
                        text = button.text_content().strip()
                        if "export" in text.lower():
                            # Check if this button is not in the Credit Overview section
                            credit_section = page.locator("div:has-text('Credit Overview')").first
                            if credit_section.count() > 0:
                                # Check if the button is not a child of the Credit Overview section
                                # This is a simplified check
                                button_html = button.evaluate("el => el.outerHTML")
                                credit_html = credit_section.evaluate("el => el.outerHTML")
                                if button_html not in credit_html:
                                    export_button = button
                                    print(f"Found Export button with text: {text} (not in Credit Overview)")
                                    break
                            else:
                                # If we can't find Credit Overview, just use this button
                                export_button = button
                                print(f"Found Export button with text: {text}")
                                break
                    except Exception as e:
                        print(f"Error checking button: {e}")
                        continue
            except Exception as e:
                print(f"Method 4 failed: {e}")

        if not export_button or export_button.count() == 0:
            raise Exception("Could not find Export button in Funding Activities section")

        print("Clicking Export button...")

        # Take a screenshot before clicking
        page.screenshot(path="before_export_click.png")
        print("Saved screenshot before clicking export button")

        try:
            # Set up download listener before clicking the button
            with page.expect_download(timeout=60000) as download_info:
                # Click with force and wait for network idle
                export_button.click(force=True)
                print("Export button clicked, waiting for download to start...")

                # Wait for any dialogs or popups that might appear
                page.wait_for_load_state("networkidle", timeout=10000)

            # Get the download object
            download = download_info.value
            print(f"Download started: {download.suggested_filename}")

            # Save the file to the downloads folder
            download_path = os.path.join(self.download_folder, download.suggested_filename)
            download.save_as(download_path)
            print(f"Download saved to: {download_path}")

            # Set the downloaded file path
            self.downloaded_file_path = download_path

            print("Export completed successfully")

        except Exception as e:
            print(f"Error during export: {e}")

            # Take a screenshot after the error
            page.screenshot(path="export_error.png")
            print("Saved screenshot after export error")

            # Try an alternative approach - sometimes the download starts without triggering the event
            print("Trying alternative approach to find the downloaded file...")
            time.sleep(10)  # Wait a bit for any download to complete

            # Look for recently modified CSV files in the downloads folder
            csv_files = glob.glob(os.path.join(self.download_folder, "*.csv"))
            if csv_files:
                # Sort by modification time to get the most recent
                csv_files.sort(key=os.path.getmtime, reverse=True)
                # Get the most recent file
                most_recent = csv_files[0]
                # Check if it was modified in the last minute
                mod_time = os.path.getmtime(most_recent)
                if time.time() - mod_time < 60:  # Less than a minute old
                    self.downloaded_file_path = most_recent
                    print(f"Found recently downloaded file: {self.downloaded_file_path}")
                    return

            # If we get here, we couldn't find a download
            raise Exception("Failed to download the export file")

    def _wait_for_download(self):
        """Verify the download completed successfully"""
        # Since we're now using Playwright's download API, we already have the file path
        # This method is now just a verification step

        if not self.downloaded_file_path:
            print("No download file path set. Attempting to find the most recent download...")
            # As a fallback, look for files matching the pattern
            downloaded_files = glob.glob(os.path.join(self.download_folder, f"*FundingActivities*.csv"))

            if downloaded_files:
                # Sort by modification time to get the most recent
                downloaded_files.sort(key=os.path.getmtime, reverse=True)
                self.downloaded_file_path = downloaded_files[0]
                print(f"Found recent download: {self.downloaded_file_path}")
            else:
                raise Exception("No downloaded file found and no path was set")

        # Verify the file exists and has content
        if not os.path.exists(self.downloaded_file_path):
            raise Exception(f"Downloaded file does not exist at path: {self.downloaded_file_path}")

        file_size = os.path.getsize(self.downloaded_file_path)
        if file_size == 0:
            raise Exception(f"Downloaded file is empty: {self.downloaded_file_path}")

        print(f"Download verified: {self.downloaded_file_path} ({file_size} bytes)")
        return

    def _move_file_to_destination(self):
        """Move the downloaded file to the destination folder"""
        if not self.downloaded_file_path:
            raise Exception("No downloaded file found")

        # Create destination folder if it doesn't exist
        os.makedirs(self.destination_folder, exist_ok=True)

        # Generate destination path with today's date
        filename = os.path.basename(self.downloaded_file_path)
        self.destination_file_path = os.path.join(self.destination_folder, filename)

        print(f"Moving file from {self.downloaded_file_path} to {self.destination_file_path}")

        # Copy the file to destination
        shutil.copy2(self.downloaded_file_path, self.destination_file_path)
        print(f"File copied to destination")

        # Optionally delete the original file
        # os.remove(self.downloaded_file_path)
        # print(f"Original file deleted")

    def _find_previous_month_file(self):
        """Find the previous month's file in the destination folder"""
        print(f"Looking for previous month's file (month: {self.previous_month_str})...")

        # Get all CSV files in the destination folder
        csv_files = glob.glob(os.path.join(self.destination_folder, "*FundingActivities*.csv"))

        if not csv_files:
            print("No previous files found. This will be treated as the first month.")
            return

        # Filter out today's file
        previous_files = [f for f in csv_files if f != self.destination_file_path]

        if not previous_files:
            print("No previous files found. This will be treated as the first month.")
            return

        # Look for files with the previous month in the filename
        previous_month_files = []
        for file_path in previous_files:
            filename = os.path.basename(file_path)
            # Look for files with pattern like "03-15-2024-FundingActivities.csv"
            # where 03 is the month
            match = re.match(r'^(\d{2})-\d{2}-\d{4}-FundingActivities\.csv$', filename)
            if match and match.group(1) == self.previous_month_str:
                previous_month_files.append(file_path)
                print(f"Found file from previous month: {filename}")

        if previous_month_files:
            # If we found files from the previous month, use the most recent one
            previous_month_files.sort(key=os.path.getmtime, reverse=True)
            self.previous_month_file = previous_month_files[0]
            print(f"Using previous month's file: {os.path.basename(self.previous_month_file)}")
        else:
            # If no files from the previous month, fall back to the most recent file
            print(f"No files found specifically from month {self.previous_month_str}.")
            print("Falling back to the most recent file.")
            previous_files.sort(key=os.path.getmtime, reverse=True)
            self.previous_month_file = previous_files[0]
            print(f"Using most recent file: {os.path.basename(self.previous_month_file)}")

        print(f"Found previous month's file: {self.previous_month_file}")

    def _compare_with_previous_month(self):
        """Compare the current month's data with the previous month"""
        print("Comparing current month's data with previous month...")

        # Load current month's data
        current_df = pd.read_csv(self.destination_file_path)
        print(f"Current month's file has {len(current_df)} records")

        # Print column names for debugging
        print("CSV file columns:")
        for col in current_df.columns:
            print(f"  - {col}")
        print()

        # Initialize the new records list
        self.new_records = []

        # Add the new columns
        current_df['New This Month'] = 'N'
        current_df['Customer'] = None
        current_df['Total Requested Cash Amount (Local Currency)'] = None
        current_df['Currency'] = None
        current_df['Converted to USD'] = None
        current_df['Finance Approval Date'] = None

        # Get the unique identifier column (Fund Request ID)
        id_column = None
        possible_id_columns = ['Fund Request ID', 'Request ID', 'ID', 'Request Id', 'Fund Request Id']

        # First try exact match
        for col in possible_id_columns:
            if col in current_df.columns:
                id_column = col
                print(f"Found ID column with exact match: {col}")
                break

        # If no exact match, try case-insensitive match
        if not id_column:
            print("No exact match for ID column, trying case-insensitive match...")
            current_cols_lower = [c.lower() for c in current_df.columns]
            for col in possible_id_columns:
                if col.lower() in current_cols_lower:
                    # Find the original column name with the correct case
                    idx = current_cols_lower.index(col.lower())
                    id_column = current_df.columns[idx]
                    print(f"Found ID column with case-insensitive match: {id_column}")
                    break

        if not id_column:
            raise Exception("Could not find ID column in the CSV file")

        print(f"Using '{id_column}' as the identifier column")

        # If no previous month file, mark all as new
        if not self.previous_month_file:
            current_df['New This Month'] = 'Y'
            self.new_records = current_df.index.tolist()
            print(f"No previous month file found. Marked all {len(current_df)} records as new.")
        else:
            # Load previous month's data
            previous_df = pd.read_csv(self.previous_month_file)
            print(f"Previous month's file has {len(previous_df)} records")

            # Print columns in previous month's file for debugging
            print("\nColumns in previous month's file:")
            for col in previous_df.columns:
                print(f"  - {col}")
            print()

            # Make sure we have the same ID column in previous month's file
            if id_column not in previous_df.columns:
                # Try to find a matching column in the previous file
                prev_id_column = None
                prev_cols_lower = [c.lower() for c in previous_df.columns]
                if id_column.lower() in prev_cols_lower:
                    idx = prev_cols_lower.index(id_column.lower())
                    prev_id_column = previous_df.columns[idx]
                    print(f"Found matching ID column in previous file: {prev_id_column}")
                else:
                    # Try all possible ID columns
                    for col in possible_id_columns:
                        if col in previous_df.columns:
                            prev_id_column = col
                            print(f"Using alternative ID column in previous file: {prev_id_column}")
                            break
                        elif col.lower() in prev_cols_lower:
                            idx = prev_cols_lower.index(col.lower())
                            prev_id_column = previous_df.columns[idx]
                            print(f"Using alternative ID column in previous file: {prev_id_column}")
                            break

                if not prev_id_column:
                    print("Warning: Could not find matching ID column in previous file.")
                    print("Treating all records as new.")
                    current_df['New This Month'] = 'Y'
                    self.new_records = current_df.index.tolist()
                    print(f"Marked all {len(current_df)} records as new.")
                else:
                    # Use the found column
                    previous_ids = set(previous_df[prev_id_column])
                    current_ids = set(current_df[id_column])
                    new_ids = current_ids - previous_ids
                    print(f"Found {len(new_ids)} new records")

                    # Print the new IDs for debugging
                    if new_ids:
                        print("New Fund Request IDs:")
                        for new_id in new_ids:
                            print(f"  - {new_id}")

                    # Mark new records
                    for idx, row in current_df.iterrows():
                        if row[id_column] in new_ids:
                            current_df.at[idx, 'New This Month'] = 'Y'
                            self.new_records.append(idx)
            else:
                # Both files have the same ID column
                current_ids = set(current_df[id_column])
                previous_ids = set(previous_df[id_column])
                new_ids = current_ids - previous_ids
                print(f"Found {len(new_ids)} new records")

                # Print the new IDs for debugging
                if new_ids:
                    print("New Fund Request IDs:")
                    for new_id in new_ids:
                        print(f"  - {new_id}")

                # Mark new records
                for idx, row in current_df.iterrows():
                    if row[id_column] in new_ids:
                        current_df.at[idx, 'New This Month'] = 'Y'
                        self.new_records.append(idx)

        # Print summary of new records
        print(f"Total new records identified: {len(self.new_records)}")

        # Copy values from previous month's file for existing records
        if self.previous_month_file:
            print("Copying values from previous month's file for existing records...")

            # Columns to copy from previous month's file
            columns_to_copy = [
                'Customer',
                'Total Requested Cash Amount (Local Currency)',
                'Currency',
                'Converted to USD',
                'Finance Approval Date'
            ]

            # Column mapping for different column names between old and new files
            column_mapping = {
                'Local Amount': 'Total Requested Cash Amount (Local Currency)',
                'Finance Approved Date': 'Finance Approval Date'
            }

            # Get the ID column in previous month's file
            prev_id_column = None
            if id_column in previous_df.columns:
                prev_id_column = id_column
            else:
                # Try to find a matching column in the previous file
                prev_cols_lower = [c.lower() for c in previous_df.columns]
                if id_column.lower() in prev_cols_lower:
                    idx = prev_cols_lower.index(id_column.lower())
                    prev_id_column = previous_df.columns[idx]
                else:
                    # Try all possible ID columns
                    for col in possible_id_columns:
                        if col in previous_df.columns:
                            prev_id_column = col
                            break
                        elif col.lower() in prev_cols_lower:
                            idx = prev_cols_lower.index(col.lower())
                            prev_id_column = previous_df.columns[idx]
                            break

            if prev_id_column:
                print(f"Using '{prev_id_column}' as the identifier column in previous month's file")

                # For each record in current month's file
                copied_count = 0
                for idx, row in current_df.iterrows():
                    current_id = row[id_column]

                    # Find matching record in previous month's file
                    prev_record = previous_df[previous_df[prev_id_column] == current_id]
                    if not prev_record.empty:
                        print(f"\nCopying values for record with ID: {current_id}")
                        # Copy values for each column
                        for col in columns_to_copy:
                            # Check if column exists in previous month's file
                            if col in previous_df.columns:
                                # Get the value from previous month's file
                                value = prev_record[col].values[0]
                                # Only copy if current value is None or NaN
                                if pd.isna(current_df.at[idx, col]) or current_df.at[idx, col] is None:
                                    print(f"  - Copying '{col}': {value}")
                                    current_df.at[idx, col] = value
                                else:
                                    print(f"  - Skipping '{col}': already has value {current_df.at[idx, col]}")
                            else:
                                print(f"  - Column '{col}' not found in previous month's file")

                        # Handle column mapping for differently named columns
                        for old_col, new_col in column_mapping.items():
                            if old_col in previous_df.columns:
                                # Get the value from the old column
                                value = prev_record[old_col].values[0]
                                # Only copy if current value is None or NaN
                                if pd.isna(current_df.at[idx, new_col]) or current_df.at[idx, new_col] is None:
                                    print(f"Copying value from '{old_col}' to '{new_col}': {value}")
                                    current_df.at[idx, new_col] = value
                        copied_count += 1

                print(f"Copied values for {copied_count} records from previous month's file")
            else:
                print("Could not find matching ID column in previous month's file. Skipping value copying.")

        # Save the updated dataframe back to the file
        current_df.to_csv(self.destination_file_path, index=False)
        print(f"Updated file with new columns and copied values")

    def _extract_additional_data(self, page):
        """Extract additional data for new records

        Args:
            page: The Playwright page object to use for navigation
        """
        if not self.new_records:
            print("No new records to extract additional data for")
            return

        print(f"Extracting additional data for {len(self.new_records)} new records...")

        # Load the current dataframe
        df = pd.read_csv(self.destination_file_path)

        # Print column names for debugging
        print("CSV file columns for additional data extraction:")
        for col in df.columns:
            print(f"  - {col}")
        print()

        # Get the ID column
        id_column = None
        possible_id_columns = ['Fund Request ID', 'Request ID', 'ID', 'Request Id', 'Fund Request Id']

        # First try exact match
        for col in possible_id_columns:
            if col in df.columns:
                id_column = col
                print(f"Found ID column with exact match: {col}")
                break

        # If no exact match, try case-insensitive match
        if not id_column:
            print("No exact match for ID column, trying case-insensitive match...")
            df_cols_lower = [c.lower() for c in df.columns]
            for col in possible_id_columns:
                if col.lower() in df_cols_lower:
                    # Find the original column name with the correct case
                    idx = df_cols_lower.index(col.lower())
                    id_column = df.columns[idx]
                    print(f"Found ID column with case-insensitive match: {id_column}")
                    break

        if not id_column:
            raise Exception("Could not find ID column in the CSV file")

        # Print the list of new records we'll be processing
        print("\nProcessing the following new Fund Request IDs:")
        for idx in self.new_records:
            fund_id = df.at[idx, id_column]
            print(f"  - {fund_id}")
        print()

        try:
            # Make sure we're on the dashboard before starting
            self._navigate_to_dashboard(page)

            # Process each new record
            processed_count = 0
            total_count = len(self.new_records)
            current_record = 0

            for idx in self.new_records:
                current_record += 1
                fund_id = df.at[idx, id_column]
                print(f"\nProcessing fund ID: {fund_id} (record {current_record} of {total_count})")
                record_start_time = time.time()

                # Navigate to the fund details page
                self._navigate_to_fund_details(page, fund_id)

                # Check if the fund request should be skipped based on status and stage
                status, stage, should_skip = self._check_fund_status(page)
                if should_skip:
                    print(f"Skipping fund ID {fund_id} with Status: {status}, Stage: {stage}")
                    # Don't increment processed_count for skipped records
                    continue

                # Extract the required data
                customer, amount, currency, approval_date = self._extract_fund_data(page)

                # No need to extract program anymore, we're using Request ID instead

                # Update the dataframe with customer based on Request ID and currency
                # 1. If Request ID contains MDF, check currency
                fund_id = df.at[idx, id_column]
                if "MDF" in fund_id.upper():
                    # Check currency
                    if currency and (currency.upper() == "CAD" or currency.upper() == "USD"):
                        df.at[idx, 'Customer'] = "Adastra CA Marketing"
                        print(f"Setting customer to 'Adastra CA Marketing' in dataframe because Request ID '{fund_id}' contains 'MDF' and currency is '{currency}'")
                    else:
                        # Keep customer blank for other currencies
                        df.at[idx, 'Customer'] = ""
                        print(f"Setting customer to blank in dataframe because Request ID '{fund_id}' contains 'MDF' but currency is not CAD/USD")
                # 2. For other cases, try to extract customer from business description using AI
                else:
                    # Extract business description
                    business_desc = self._extract_business_description(page)
                    if business_desc:
                        # Try to extract customer from business description using AI
                        customer_from_ai = self._extract_customer_from_description(business_desc)
                        if customer_from_ai:
                            df.at[idx, 'Customer'] = customer_from_ai
                            print(f"Setting customer to '{customer_from_ai}' in dataframe (extracted using AI)")
                        else:
                            df.at[idx, 'Customer'] = ""
                            print(f"Setting customer to blank in dataframe (AI extraction failed)")
                    else:
                        df.at[idx, 'Customer'] = ""
                        print(f"Setting customer to blank in dataframe (no business description found)")

                df.at[idx, 'Total Requested Cash Amount (Local Currency)'] = amount
                df.at[idx, 'Currency'] = currency

                # Calculate converted amount (simplified)
                converted_amount = self._convert_to_usd(amount, currency)
                df.at[idx, 'Converted to USD'] = converted_amount

                df.at[idx, 'Finance Approval Date'] = approval_date

                # Print extraction results
                if approval_date:
                    print(f"Extracted data for {fund_id}: Customer={customer}, Amount={amount}, Currency={currency}, Finance Approval Date={approval_date}")
                else:
                    print(f"Extracted data for {fund_id}: Customer={customer}, Amount={amount}, Currency={currency}, Finance Approval Date=NOT FOUND")

                # Save after each record in case of errors
                df.to_csv(self.destination_file_path, index=False)
                print(f"Saved progress after processing {fund_id}")

                # Increment the processed count
                processed_count += 1

                # Calculate and display processing time
                record_end_time = time.time()
                processing_time = record_end_time - record_start_time
                print(f"Processing time for {fund_id}: {processing_time:.2f} seconds")
                print(f"Completed processing record {current_record} of {total_count}, processed count: {processed_count}")

                # Debug print to show the current record number and total count
                if current_record < total_count:
                    print(f"Moving to record {current_record + 1} of {total_count}")

                # Return to dashboard for next record
                if idx != self.new_records[-1]:  # If not the last record
                    print("Returning to dashboard for next record...")
                    self._navigate_to_dashboard(page)

        except Exception as e:
            print(f"Error extracting additional data: {e}")
            # Take a screenshot to help with debugging
            try:
                page.screenshot(path="extract_data_error.png")
                print("Saved screenshot to extract_data_error.png for debugging")
            except:
                pass

        # Final save of the updated dataframe
        df.to_csv(self.destination_file_path, index=False)
        skipped_count = len(self.new_records) - processed_count
        print(f"\nCompleted extracting additional data for {processed_count} out of {len(self.new_records)} new records")
        print(f"Processed: {processed_count}, Skipped: {skipped_count}")
        print(f"Updated file saved to: {self.destination_file_path}")

    def _navigate_to_fund_details(self, page, fund_id):
        """Navigate to the fund details page for a specific fund ID"""
        print(f"Navigating to fund details for {fund_id}...")

        # First try to search for the fund ID (case insensitive)
        try:
            # Look for search box
            search_box = page.locator("input[type='search'], input[placeholder*='search']").first
            if search_box.count() > 0:
                search_box.fill(fund_id)
                search_box.press("Enter")
                time.sleep(3)  # Wait for search results

                # Look for the fund ID link
                fund_link = page.locator(f"a:has-text('{fund_id}')").first
                if fund_link.count() > 0:
                    fund_link.click()
                    page.wait_for_load_state("networkidle", timeout=30000)
                    time.sleep(3)
                    return
        except Exception as e:
            print(f"Error searching for fund ID: {e}")

        # If search fails, try direct navigation with different URL patterns
        try:
            # Check if the fund ID contains 'tvbm' (case insensitive)
            if 'tvbm' in fund_id.lower():
                # Use the fund-requests/ID/details pattern
                url = f"https://funding.awspartner.com/fund-requests/{fund_id}/details"
            else:
                # Use the fund-request/ID/review pattern
                url = f"https://funding.awspartner.com/fund-request/{fund_id}/review"

            print(f"Trying to navigate to: {url}")
            page.goto(url)
            page.wait_for_load_state("networkidle", timeout=30000)
            time.sleep(3)

            # Check if we landed on the correct page
            if "not found" in page.title().lower() or "error" in page.title().lower():
                # Try the alternative URL pattern
                if 'tvbm' in fund_id.lower():
                    alt_url = f"https://funding.awspartner.com/fund-request/{fund_id}/review"
                else:
                    alt_url = f"https://funding.awspartner.com/fund-requests/{fund_id}/details"

                print(f"First URL failed, trying alternative: {alt_url}")
                page.goto(alt_url)
                page.wait_for_load_state("networkidle", timeout=30000)
                time.sleep(3)

                # Also try without the /review or /details suffix as a last resort
                if "not found" in page.title().lower() or "error" in page.title().lower():
                    base_url1 = f"https://funding.awspartner.com/fund-request/{fund_id}"
                    base_url2 = f"https://funding.awspartner.com/fund-requests/{fund_id}"

                    print(f"Trying base URL: {base_url1}")
                    page.goto(base_url1)
                    page.wait_for_load_state("networkidle", timeout=30000)
                    time.sleep(3)

                    if "not found" in page.title().lower() or "error" in page.title().lower():
                        print(f"Trying base URL: {base_url2}")
                        page.goto(base_url2)
                        page.wait_for_load_state("networkidle", timeout=30000)
                        time.sleep(3)
        except Exception as e:
            print(f"Error navigating directly to fund details: {e}")
            raise Exception(f"Could not navigate to fund details for {fund_id}")

        # Verify we're on a fund details page
        fund_details_indicators = [
            "Fund Request Details",
            "Fund Request Information",
            "Request Information",
            "Funding Request"
        ]

        for indicator in fund_details_indicators:
            indicator_locator = page.locator(f"text={indicator}").first
            if indicator_locator.count() > 0:
                print(f"Successfully navigated to fund details page (found indicator: {indicator})")
                return

        print("Warning: Could not verify we're on the fund details page")
        # Continue anyway as we've tried all possible URLs

    def _check_fund_status(self, page):
        """Check the status and stage of the fund request

        Returns:
            tuple: (status, stage, should_skip) where should_skip is a boolean indicating if this record should be skipped
        """
        print("Checking fund request status and stage...")

        # Wait for content to load
        page.wait_for_timeout(2000)

        status = "Unknown"
        stage = "Unknown"

        # Get the latest stage from the Fund Request History table (top row)
        # This is used to determine whether to skip the record
        stage = self._get_latest_stage_from_history(page)
        if stage != "Unknown":
            print(f"Found latest stage from Fund Request History: {stage}")
        else:
            print("Could not find latest stage in Fund Request History")

        # Extract Active Status - specifically look for the element with text "Active Status"
        try:
            # Look for the exact "Active Status" label as shown in the screenshot
            active_status_label = page.locator("text=Active Status").first
            if active_status_label.count() > 0:
                # Try different approaches to get the status value

                # Approach 1: Look for a nearby element with the status
                try:
                    # Look for elements near the label
                    status_element = page.locator("text=Active Status").locator("xpath=following::*[1]").first
                    if status_element.count() > 0:
                        status = status_element.text_content().strip()
                        print(f"Found Active Status: {status}")
                except Exception as e:
                    print(f"Error getting Active Status with approach 1: {e}")

                # Approach 2: Look for elements with specific classes or attributes
                if status == "Unknown":
                    try:
                        # Look for elements that might contain the status (like a green badge)
                        status_badges = page.locator("div:has-text('Active Status') + div, span.badge, div.badge").all()
                        for badge in status_badges:
                            badge_text = badge.text_content().strip()
                            if badge_text and badge_text not in ["Active Status"]:
                                status = badge_text
                                print(f"Found Active Status badge: {status}")
                                break
                    except Exception as e:
                        print(f"Error getting Active Status with approach 2: {e}")

                # Approach 3: Look for a green badge element (as shown in the screenshot)
                if status == "Unknown":
                    try:
                        # Look for elements with green background that might contain the status
                        green_elements = page.locator("div.badge-success, span.badge-success, div[style*='background-color: green'], span[style*='background-color: green'], div.badge, span.badge").all()
                        for elem in green_elements:
                            elem_text = elem.text_content().strip()
                            if elem_text and elem_text not in ["Active Status"]:
                                status = elem_text
                                print(f"Found Active Status badge: {status}")
                                break
                    except Exception as e:
                        print(f"Error getting Active Status with approach 3: {e}")

                # Approach 4: Look specifically for any text containing 'Completed'
                if status == "Unknown":
                    try:
                        # Look for any element containing 'Completed' text
                        completed_elements = page.locator("text=Completed").all()
                        for completed_elem in completed_elements:
                            try:
                                # Get the text content
                                elem_text = completed_elem.text_content().strip()
                                if "completed" in elem_text.lower():
                                    # Check if this is a status indicator (not just any text with 'completed')
                                    js_code = '''
                                    (el) => {
                                        let parent = el.parentElement;
                                        return parent ? parent.textContent.trim() : '';
                                    }
                                    '''
                                    parent_text = completed_elem.evaluate(js_code)

                                    # If the parent doesn't contain much more text, it's likely a status badge
                                    if len(parent_text) < 30 or "status" in parent_text.lower():
                                        status = "Completed"
                                        print(f"Found 'Completed' text: {elem_text}")
                                        break
                            except Exception as inner_e:
                                print(f"Error processing completed element: {inner_e}")
                                continue
                    except Exception as e:
                        print(f"Error checking for Completed text: {e}")

                # Approach 4: Take a screenshot to help with debugging
                if status == "Unknown":
                    try:
                        page.screenshot(path="active_status_section.png")
                        print("Saved screenshot of Active Status section for debugging")
                    except:
                        pass
        except Exception as e:
            print(f"Error finding Active Status label: {e}")

        # We're now focusing on extracting the stage from Fund Request History table only
        # No need to extract Progress Stages separately

        # Take a screenshot if we couldn't find the stage
        if stage == "Unknown":
            try:
                print("Could not determine stage, taking screenshot for debugging...")
                page.screenshot(path="fund_request_history_debug.png")
                print("Saved screenshot to fund_request_history_debug.png for debugging")
            except Exception as e:
                print(f"Error taking screenshot: {e}")

        # Determine if we should skip this record based on the specified conditions
        should_skip = False

        # Convert status and stage to lowercase for case-insensitive comparison
        status_lower = status.lower()
        stage_lower = stage.lower()

        # Check the conditions for skipping
        # 1. Status = terminated, Stage = any
        if status_lower == "terminated":
            should_skip = True
            print(f"Skipping: Status is 'terminated' (Stage={stage}, Status={status})")
        # 2. Status = active, Stage = created
        elif status_lower == "active" and stage_lower == "created":
            should_skip = True
            print(f"Skipping: Status is 'active' and Stage is 'created' (Stage={stage}, Status={status})")
        # 3. Status = draft, Stage = any
        elif status_lower == "draft":
            should_skip = True
            print(f"Skipping: Status is 'draft' (Stage={stage}, Status={status})")
        # 4. Status = expired, Stage = any
        elif status_lower == "expired":
            should_skip = True
            print(f"Skipping: Status is 'expired' (Stage={stage}, Status={status})")
        # Special case: Don't skip if Status is Completed and Stage is Completed
        elif status_lower == "completed" and stage_lower == "completed":
            should_skip = False
            print(f"Not skipping: Status and Stage are both 'Completed' (Stage={stage}, Status={status})")
        # Special case: Handle 'complated' typo in stage name
        elif status_lower == "completed" and "compl" in stage_lower:
            should_skip = False
            print(f"Not skipping: Status is 'Completed' and Stage contains 'compl' (Stage={stage}, Status={status})")

        if not should_skip:
            print(f"Not skipping: Stage={stage}, Status={status}")

        return status, stage, should_skip

    def _extract_program(self, page):
        """Extract the program from the fund details page"""
        # Wait for content to load
        page.wait_for_timeout(1000)

        program = None
        program_labels = [
            "Program",
            "Program Type",
            "Fund Program",
            "Fund Type",
        ]

        for label in program_labels:
            program_locator = page.locator(f"text={label}").first
            if program_locator.count() > 0:
                # Try to get the next element which should contain the program
                try:
                    next_elem = program_locator.locator("xpath=following::*[1]").first
                    if next_elem.count() > 0:
                        program = next_elem.text_content().strip()
                        print(f"Found program: {program}")
                        break
                except Exception as e:
                    print(f"Error getting program with label '{label}': {e}")

        return program

    def _extract_business_description(self, page):
        """Extract the business description text from the page"""
        # Wait for content to load
        page.wait_for_timeout(1000)

        full_desc = None
        business_desc_labels = [
            "Business Description",
            "Business Case",
            "Project Description",
            "Description",
            "Customer Description",
        ]

        for label in business_desc_labels:
            desc_locator = page.locator(f"text={label}").first
            if desc_locator.count() > 0:
                # Try to get the next element which should contain the business description
                try:
                    next_elem = desc_locator.locator("xpath=following::*[1]").first
                    if next_elem.count() > 0:
                        full_desc = next_elem.text_content().strip()
                        print(f"Found business description: {full_desc[:100]}...")
                        break
                except Exception as e:
                    print(f"Error getting business description with label '{label}': {e}")

        return full_desc

    def _extract_customer_from_description(self, description):
        """Extract customer name from a business description using AI or regex patterns.

        Args:
            description: The business description text.

        Returns:
            Extracted customer name or empty string if not found.
        """
        if not description:
            return ""

        # Try using AI first if available
        if self.ai_helper is not None:
            try:
                customer = self.ai_helper.extract_customer_from_description(description)
                if customer:
                    print(f"AI extracted customer: {customer}")
                    return customer
            except Exception as e:
                print(f"Error using AI to extract customer: {e}")

        # Fallback to regex patterns
        customer_patterns = [
            r"Customer:\s*([^\n.,]+)",
            r"Client:\s*([^\n.,]+)",
            r"Customer name:\s*([^\n.,]+)",
            r"Client name:\s*([^\n.,]+)",
            r"([^\n.,]+)" # Just take the first part before a period or newline as fallback
        ]

        for pattern in customer_patterns:
            match = re.search(pattern, description, re.IGNORECASE)
            if match:
                customer = match.group(1).strip()
                print(f"Regex extracted customer from description: {customer}")
                return customer

        return ""

    def _extract_fund_data(self, page):
        """Extract the required data from the fund details page:
        1. Customer
        2. Total Requested MDF Cash Amount (Local Currency)
        3. Currency
        4. Finance Approval Date (from any row in Fund Request History)
        """
        # This extracts data for records that shouldn't be skipped

        # Wait for content to load
        page.wait_for_timeout(3000)

        # No need to extract program anymore, we're using Request ID instead

        # Extract currency early so we can use it for customer determination
        currency = None
        currency_labels = [
            "Currency",
            "Local Currency",
            "Currency Code",
        ]

        for label in currency_labels:
            currency_locator = page.locator(f"text={label}").first
            if currency_locator.count() > 0:
                # Try to get the next element which should contain the currency
                try:
                    next_elem = currency_locator.locator("xpath=following::*[1]").first
                    if next_elem.count() > 0:
                        currency = next_elem.text_content().strip()
                        print(f"Found currency: {currency}")
                        break
                except Exception as e:
                    print(f"Error getting currency with label '{label}': {e}")

        # We'll set the customer in the _process_new_records method based on Request ID and currency
        # Here we'll just return a placeholder value
        customer = ""  # Empty string as placeholder
        print("Customer will be determined based on Request ID and currency in _process_new_records method")

        # No need to extract customer here anymore

        # Extract amount
        amount = None
        amount_labels = [
            "Total Requested Cash Amount (Local Currency)",
            "Total Requested MDF Cash Amount (Local Currency)",
            "Requested MDF Cash Funding (Local Currency)",
            "Requested Cash Funding Amount (Local Currency)",
            "Claim Amount (Local Currency)",
            "Requested Cash Funding Amount",
            "Cash Funding Amount",
            "Funding Amount",
        ]

        for label in amount_labels:
            amount_locator = page.locator(f"text={label}").first
            if amount_locator.count() > 0:
                # Try to get the next element which should contain the amount
                try:
                    next_elem = amount_locator.locator("xpath=following::*[1]").first
                    if next_elem.count() > 0:
                        amount = next_elem.text_content().strip()
                        print(f"Found amount: {amount}")
                        break
                except:
                    pass

        # Currency is already extracted above

        # Extract finance approval date
        approval_date = self._find_finance_approval_date(page)

        # If we couldn't extract a customer, use a default value
        if customer is None:
            customer = "Unknown Customer"
            print(f"Using default customer: {customer}")

        return customer, amount, currency, approval_date

    def _get_latest_stage_from_history(self, page):
        """Extract the latest stage from the Fund Request History table (top row).
        This is used to determine whether to skip the record based on the stage and status.
        It only looks at the first row (latest record) in the Fund Request History table.
        """
        print("Looking for latest stage in Fund Request History to determine skip status...")

        # Look for Fund Request History section
        history_section = page.locator("text=Fund Request History").first
        if history_section.count() == 0:
            print("Could not find Fund Request History section")
            return "Unknown"

        # Find the table
        table = page.locator("table").first
        if table.count() == 0:
            print("Could not find table in Fund Request History section")
            return "Unknown"

        # Find the Stage column index
        headers = table.locator("thead th").all()
        stage_column_idx = None

        for i, header in enumerate(headers):
            header_text = header.text_content().strip().lower()
            if "stage" in header_text:
                stage_column_idx = i
                print(f"Found Stage column at index {stage_column_idx}")
                break

        if stage_column_idx is None:
            print("Could not find Stage column in Fund Request History table")
            return "Unknown"

        # Get the first row (latest record)
        rows = table.locator("tbody tr").all()
        if not rows:
            print("No rows found in Fund Request History table")
            return "Unknown"

        # Extract stage from the first row
        first_row = rows[0]
        cells = first_row.locator("td").all()

        if len(cells) <= stage_column_idx:
            print(f"Stage column index {stage_column_idx} is out of range for row with {len(cells)} cells")
            return "Unknown"

        stage_cell = cells[stage_column_idx]
        stage_text = stage_cell.text_content().strip()

        if not stage_text:
            print("Stage cell is empty")
            return "Unknown"

        print(f"Found latest stage in Fund Request History: {stage_text}")
        return stage_text

    # We've removed the _find_latest_stage_date method as we're now focusing only on finance approval date

    def _find_finance_approval_date(self, page, current_page=1, max_pages=3):
        """Find the Finance Approval date specifically in the Fund Request History section.
        This is used for data extraction to fill in the Finance Approval Date column in the CSV.
        It searches rows in the Fund Request History table across multiple pages (up to max_pages).
        """
        print(f"Looking for Finance Approval date for data extraction (page {current_page} of max {max_pages})...")

        # Look for Fund Request History section
        history_section = page.locator("text=Fund Request History").first
        if history_section.count() == 0:
            print("Could not find Fund Request History section")
            return None

        # Find the table
        table = page.locator("table").first
        if table.count() == 0:
            print("Could not find table in Fund Request History section")
            return None

        # Find the Stage column index and Date column index
        headers = table.locator("thead th").all()
        stage_column_idx = None
        date_column_idx = None
        action_column_idx = None

        for i, header in enumerate(headers):
            header_text = header.text_content().strip().lower()
            if "stage" in header_text:
                stage_column_idx = i
                print(f"Found Stage column at index {stage_column_idx}")
            elif "date" in header_text:
                date_column_idx = i
                print(f"Found Date column at index {date_column_idx}")
            elif "action" in header_text:
                action_column_idx = i
                print(f"Found Action column at index {action_column_idx}")

        if stage_column_idx is None or date_column_idx is None:
            print("Could not find Stage or Date column")
            return None

        # Check all rows for Finance Approval
        rows = table.locator("tbody tr").all()
        for row in rows:
            cells = row.locator("td").all()

            if len(cells) <= stage_column_idx or len(cells) <= date_column_idx:
                continue

            date_cell = cells[date_column_idx]
            stage_cell = cells[stage_column_idx]
            action_cell = cells[action_column_idx] if action_column_idx is not None and len(cells) > action_column_idx else None

            date_text = date_cell.text_content().strip()
            stage_text = stage_cell.text_content().strip().lower()
            action_text = action_cell.text_content().strip().lower() if action_cell else ""

            # Check if this is the finance approval row
            # Looking for Stage="finance_approval" and optionally Action="ChangedStage"
            if "finance" in stage_text:
                print(f"Found Finance Approval date: {date_text}")
                return date_text
            elif action_column_idx is not None and "changedstage" in action_text and "finance" in stage_text:
                print(f"Found Finance Approval date from ChangedStage action: {date_text}")
                return date_text

            # Store business_approval date as a fallback (we'll use it if finance_approval is not found)
            if current_page == 1 and "business" in stage_text and not hasattr(self, '_business_approval_date'):
                self._business_approval_date = date_text
                print(f"Found Business Approval date as potential fallback: {date_text}")
                # Note: We don't return here, we continue looking for finance_approval

        # If we didn't find it on the first page, try pagination (up to max_pages)
        if current_page < max_pages:
            next_button = page.locator("span[class*='angle-right']").first
            if next_button.count() > 0 and "disabled" not in (next_button.get_attribute("class") or ""):
                print(f"Clicking next page button (moving to page {current_page + 1})...")
                next_button.click()
                time.sleep(2)

                # Recursive call to check the next page
                result = self._find_finance_approval_date(page, current_page + 1, max_pages)
                if result:
                    return result
            else:
                print(f"No more pages available after page {current_page}")
        else:
            print(f"Reached maximum number of pages to check ({max_pages})")

        # If we've checked all pages or reached the max_pages limit
        if current_page == 1:  # Only print this message once (when returning from the first call)
            # Check if we have a business_approval date as fallback
            if hasattr(self, '_business_approval_date'):
                business_date = self._business_approval_date
                print(f"⚠️ Finance Approval date not found. Using Business Approval date as fallback: {business_date}")
                # Clear the stored date for the next record
                delattr(self, '_business_approval_date')
                return business_date
            else:
                print("⚠️ Finance Approval date not found after checking all available pages. Skipping to next record.")
        return None

    def _get_conversion_rates(self):
        """Get the currency conversion rates"""
        # Simple conversion rates (in a real scenario, you would use an API)
        return {
            'USD': 1.0,
            'CAD': 0.74,
            'EUR': 1.09,
            'GBP': 1.31,
            'AUD': 0.67,
            'JPY': 0.0067,
            'CNY': 0.14,
            'INR': 0.012,
            'MXN': 0.059,
            'SGD': 0.75,
            'CHF': 1.13,
            'BRL': 0.18,
            # Add more currencies as needed
        }

    def _convert_to_usd(self, amount, currency):
        """Convert the amount to USD"""
        if not amount or not currency:
            return None

        # Remove any non-numeric characters from amount
        amount_str = re.sub(r'[^\d.]', '', str(amount))

        try:
            amount_value = float(amount_str)
        except:
            print(f"Could not convert amount to float: {amount}")
            return None

        # Get the conversion rates
        conversion_rates = self._get_conversion_rates()

        # Get the conversion rate
        rate = conversion_rates.get(currency.upper(), None)

        if rate is None:
            print(f"No conversion rate found for currency: {currency}")
            return None

        # Convert to USD
        usd_amount = amount_value * rate

        # Format with 2 decimal places
        return f"{usd_amount:.2f}"

    def _categorize_fund_requests(self, df):
        """Categorize fund requests using AI and add a Category column to the dataframe"""
        if self.ai_helper is None:
            print("AI Helper not available. Skipping fund request categorization.")
            return df

        print("\nCategorizing fund requests using AI...")

        # Add a Category column if it doesn't exist
        if 'Category' not in df.columns:
            df['Category'] = None

        # Get the program and description columns
        program_col = None
        desc_col = None

        # Try to find program column
        for col in df.columns:
            if 'program' in col.lower():
                program_col = col
                break

        # Try to find description column
        for col in df.columns:
            if 'description' in col.lower() or 'details' in col.lower():
                desc_col = col
                break

        if not program_col and not desc_col:
            print("Could not find program or description columns. Skipping categorization.")
            return df

        # Categorize each fund request
        categorized_count = 0
        for idx, row in df.iterrows():
            # Skip if already categorized
            if not pd.isna(df.at[idx, 'Category']) and df.at[idx, 'Category']:
                continue

            program = row.get(program_col, "") if program_col else ""
            description = row.get(desc_col, "") if desc_col else ""

            if not program and not description:
                continue

            try:
                result = self.ai_helper.categorize_fund_request(description, program)
                if result and 'category' in result:
                    df.at[idx, 'Category'] = result['category']
                    categorized_count += 1
            except Exception as e:
                print(f"Error categorizing fund request: {e}")

        print(f"Categorized {categorized_count} fund requests")
        return df

    def _save_processed_file(self):
        """Save the processed file with a timestamp"""
        # Load the current dataframe
        df = pd.read_csv(self.destination_file_path)

        # Categorize fund requests using AI if available
        df = self._categorize_fund_requests(df)

        # Calculate totals and add summary information
        try:
            # Convert the column to numeric, coercing errors to NaN
            df['Converted to USD'] = pd.to_numeric(df['Converted to USD'], errors='coerce')

            # Calculate the overall sum, ignoring NaN values
            usd_total = df['Converted to USD'].sum()

            # Print the overall total
            print(f"\n===== SUMMARY =====")
            print(f"Total Converted to USD: ${usd_total:,.2f}")

            # Calculate totals by stage if the Stage column exists
            if 'Stage' in df.columns:
                print(f"\nTotals by Stage:")
                stage_totals = df.groupby('Stage')['Converted to USD'].sum()
                for stage, total in stage_totals.items():
                    if not pd.isna(stage) and stage:
                        print(f"  {stage}: ${total:,.2f}")

            # Calculate totals by category if the Category column exists and has values
            if 'Category' in df.columns and not df['Category'].isna().all():
                print(f"\nTotals by Category:")
                category_totals = df.groupby('Category')['Converted to USD'].sum()
                for category, total in category_totals.items():
                    if not pd.isna(category) and category:
                        print(f"  {category}: ${total:,.2f}")

            # Calculate totals for new records this month
            if 'New This Month' in df.columns:
                new_records = df[df['New This Month'] == 'Y']
                if not new_records.empty:
                    new_total = new_records['Converted to USD'].sum()
                    print(f"\nNew This Month: ${new_total:,.2f}")

            print(f"=====================\n")

            # We're not adding a total row to the main dataframe anymore
            # Just keeping the summary information in the console output and summary sheet
        except Exception as e:
            print(f"Error calculating totals: {e}")

        # Create a timestamped filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        processed_filename = f"processed_fund_requests_{timestamp}.xlsx"
        processed_filepath = os.path.join(self.destination_folder, processed_filename)

        # Save to Excel with a summary sheet
        try:
            # Create a new Excel writer
            with pd.ExcelWriter(processed_filepath, engine='openpyxl') as writer:
                # Write the main data to the first sheet
                df.to_excel(writer, sheet_name='Fund Requests', index=False)

                # Create a summary sheet with more detailed information
                summary_data = []

                # Add header with timestamp
                current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                summary_data.append(['AWS Partner Funding Summary', ''])
                summary_data.append(['Generated on', current_date])
                summary_data.append(['', ''])

                # Add overall statistics
                summary_data.append(['OVERALL STATISTICS', ''])
                summary_data.append(['Total Records', len(df)])
                summary_data.append(['Total Converted to USD', f'${usd_total:,.2f}'])

                # Add new records statistics if available
                if 'New This Month' in df.columns:
                    new_records = df[df['New This Month'] == 'Y']
                    if not new_records.empty:
                        new_count = len(new_records)
                        new_total = new_records['Converted to USD'].sum()
                        summary_data.append(['New Records This Month', new_count])
                        summary_data.append(['New Amount This Month', f'${new_total:,.2f}'])

                # Add stage totals if available
                if 'Stage' in df.columns:
                    summary_data.append(['', ''])
                    summary_data.append(['TOTALS BY STAGE', ''])
                    stage_counts = df.groupby('Stage').size()
                    stage_totals = df.groupby('Stage')['Converted to USD'].sum()

                    for stage in stage_totals.index:
                        if not pd.isna(stage) and stage:
                            count = stage_counts.get(stage, 0)
                            total = stage_totals.get(stage, 0)
                            summary_data.append([f'{stage} (Count: {count})', f'${total:,.2f}'])

                # Add category totals if available
                if 'Category' in df.columns and not df['Category'].isna().all():
                    summary_data.append(['', ''])
                    summary_data.append(['TOTALS BY CATEGORY', ''])
                    category_counts = df.groupby('Category').size()
                    category_totals = df.groupby('Category')['Converted to USD'].sum()

                    for category in category_totals.index:
                        if not pd.isna(category) and category:
                            count = category_counts.get(category, 0)
                            total = category_totals.get(category, 0)
                            summary_data.append([f'{category} (Count: {count})', f'${total:,.2f}'])

                # Create a DataFrame from the summary data
                summary_df = pd.DataFrame(summary_data, columns=['Category', 'Value'])

                # Write the summary to a separate sheet
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

                # Add Currency Exchange Rate sheet with only USD, CAD, and EUR
                conversion_rates = self._get_conversion_rates()
                exchange_data = []
                exchange_data.append(['Currency', 'Rate to USD', 'Example Conversion'])

                # Filter for only USD, CAD, and EUR
                selected_currencies = ['USD', 'CAD', 'EUR']
                for currency in selected_currencies:
                    if currency in conversion_rates:
                        rate = conversion_rates[currency]
                        example_amount = 1000
                        example_conversion = example_amount * rate
                        exchange_data.append([currency, rate, f"{example_amount} {currency} = {example_conversion:.2f} USD"])

                # Create a DataFrame for the exchange rates
                exchange_df = pd.DataFrame(exchange_data[1:], columns=exchange_data[0])

                # Write the exchange rates to a separate sheet
                exchange_df.to_excel(writer, sheet_name='Currency Exchange Rates (USD, CAD, EUR)', index=False)

                # Get the summary worksheet
                summary_sheet = writer.sheets['Summary']

                # Format the summary sheet
                try:
                    # Import openpyxl styles
                    from openpyxl.styles import Font

                    # Define styles
                    header_font = Font(bold=True, size=14)
                    normal_font = Font(size=11)

                    # Apply styles to cells
                    for row in summary_sheet.iter_rows():
                        for cell in row:
                            # Skip header row (Excel adds its own)
                            if cell.row == 1:
                                continue

                            # Adjust for Excel's 1-based indexing and the header row
                            df_row = cell.row - 2

                            if df_row >= 0 and df_row < len(summary_data):
                                # Headers (AWS Partner Funding Summary, OVERALL STATISTICS, TOTALS BY STAGE)
                                if summary_data[df_row][0] in ['AWS Partner Funding Summary', 'OVERALL STATISTICS', 'TOTALS BY STAGE']:
                                    cell.font = header_font
                                # Normal rows
                                else:
                                    cell.font = normal_font

                    # Auto-adjust column width
                    for column in summary_sheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            if cell.value:
                                max_length = max(max_length, len(str(cell.value)))
                        adjusted_width = max_length + 2
                        summary_sheet.column_dimensions[column_letter].width = adjusted_width
                    # Format the Currency Exchange Rates sheet
                    exchange_sheet = writer.sheets['Currency Exchange Rates (USD, CAD, EUR)']

                    # Apply styles to cells
                    for row in exchange_sheet.iter_rows():
                        for cell in row:
                            # Make the header row bold
                            if cell.row == 1:
                                cell.font = header_font
                            else:
                                cell.font = normal_font

                    # Auto-adjust column width
                    for column in exchange_sheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            if cell.value:
                                max_length = max(max_length, len(str(cell.value)))
                        adjusted_width = max_length + 2
                        exchange_sheet.column_dimensions[column_letter].width = adjusted_width
                except Exception as e:
                    print(f"Warning: Could not apply Excel formatting: {e}")

            print(f"Processed data saved to {processed_filepath} with summary sheet")
        except Exception as e:
            # Fallback to simple Excel save if the above fails
            print(f"Error creating Excel with summary sheet: {e}")
            df.to_excel(processed_filepath, index=False)
            print(f"Processed data saved to {processed_filepath} (without summary sheet)")

        # Also save a CSV version
        csv_filepath = os.path.join(self.destination_folder, f"processed_fund_requests_{timestamp}.csv")
        df.to_csv(csv_filepath, index=False)
        print(f"CSV backup saved to {csv_filepath}")


# Run the export and comparison
if __name__ == "__main__":
    print("Starting Fund Request Export and Comparison")

    # Check if aws_session.json exists
    if not os.path.exists("aws_session.json"):
        print("aws_session.json not found. Please make sure you have a valid session file.")
        exit(1)

    exporter = FundRequestExportComparer(headless=False)
    exporter.run()
