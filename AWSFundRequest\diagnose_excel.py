import pandas as pd
import os
import sys

def check_file(file_path):
    """Check if a file exists and can be accessed."""
    print(f"\nChecking file: {file_path}")
    if not os.path.exists(file_path):
        print(f"ERROR: File does not exist at path: {file_path}")
        return False

    if not os.access(file_path, os.R_OK):
        print(f"ERROR: File exists but cannot be read (permission issue): {file_path}")
        return False

    print(f"SUCCESS: File exists and is readable: {file_path}")
    return True

def check_excel_file(file_path):
    """Check if a file is a valid Excel file and examine its structure."""
    if not check_file(file_path):
        return

    try:
        # Try to open as Excel file
        excel = pd.ExcelFile(file_path)
        sheet_names = excel.sheet_names
        print(f"Excel file contains {len(sheet_names)} sheets: {sheet_names}")

        # Check each sheet
        for sheet_name in sheet_names:
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                print(f"  Sheet '{sheet_name}' contains {len(df)} rows and {len(df.columns)} columns")
                print(f"  Columns: {df.columns.tolist()}")

                # Check for specific columns
                for col_name in ["gtm_customer_name", "Adastra AE Name", "Account Name", "Owner"]:
                    if col_name in df.columns:
                        print(f"    ✓ Found column: {col_name}")
                        # Show a few sample values
                        if not df[col_name].empty:
                            sample = df[col_name].dropna().head(3).tolist()
                            print(f"      Sample values: {sample}")

            except Exception as e:
                print(f"  ERROR reading sheet '{sheet_name}': {e}")

    except Exception as e:
        print(f"ERROR: Failed to open as Excel file: {e}")

def main():
    # Define file paths
    target_file_path = r"C:\Users\<USER>\Downloads\Fuzzy Match Target - ENT GFC Propensity List OCT.xlsx"
    account_list_file_path = r"C:\Users\<USER>\Downloads\JT - CA & US Accounts 2025_05_02 2-44-05 PM.xlsx"

    print("=== Excel File Diagnostic Tool ===")

    # Check target file
    print("\n=== Checking Target File ===")
    check_excel_file(target_file_path)

    # Check account list file
    print("\n=== Checking Account List File ===")
    check_excel_file(account_list_file_path)

    print("\n=== Diagnostic Complete ===")

if __name__ == "__main__":
    main()
